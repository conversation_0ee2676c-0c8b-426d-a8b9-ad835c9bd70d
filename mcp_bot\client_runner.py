from mcp_bot.client import MCPMultiServerClient, MCPServerSpec
from dotenv import load_dotenv
from helper.logger_setup import setup_logger
from config.settings import Config

load_dotenv()
logging = setup_logger('client_runner', 'client_runner.log')



async def run_agent(number: str, query: str) -> str:
    """
    Run the MCP agent for a given WhatsApp number and query.
    Reusable in other services (e.g., FastAPI, Flask, webhook handlers).
    """
    logging.info(f"run_agent called for number={number} with query={query!r}")
    servers = [MCPServerSpec(path=Config.SERVER_PATH)]
    client = MCPMultiServerClient(servers=servers, thread_id=number)

    try:
        await client.connect()
        result = await client.ainvoke(query)
        logging.info(F"INCOMING NUMBER: {number} - INPUT QUERY: {query} - OUTPUT: {result}.")
        return result
    except Exception as e:
        logging.error(f"Exception in run_agent: {e}", exc_info=True)
        raise
    finally:
        await client.close()
