from flask import Flask
from config.settings import Config
from app.routes import setup_routes
import os

def create_app():
    APP_ROOT = os.path.dirname(os.path.abspath(__file__)) # Get the directory of the current file (__init__.py)
    PROJECT_ROOT = os.path.dirname(APP_ROOT) # Go one level up to the project root
    TEMPLATE_DIR = os.path.join(PROJECT_ROOT, 'templates')
    STATIC_DIR = os.path.join(PROJECT_ROOT, 'static')

    app = Flask(__name__, template_folder=TEMPLATE_DIR, static_folder=STATIC_DIR)
    app.secret_key = os.urandom(24)
    app.config.from_object(Config)

    setup_routes(app)

    return app
