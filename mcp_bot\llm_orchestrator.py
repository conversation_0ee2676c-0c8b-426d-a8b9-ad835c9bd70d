import os
from dotenv import load_dotenv

# <PERSON><PERSON>hain LLMs
from langchain_openai import ChatOpenAI
from langchain_google_genai import ChatGoogleGenerativeAI
load_dotenv()
def get_llm(provider, callbacks=None):
    """
    Returns an LLM instance (OpenAI or Gemini) based on the LLM_PROVIDER env variable.
    - LLM_PROVIDER: "openai" or "gemini" (default: "openai")
    - OPENAI_API_KEY, OPENAI_MODEL required for OpenAI
    - GEMINI_API_KEY, GEMINI_MODEL required for Gemini
    """
    
    if provider == "openai":
        api_key = os.getenv("OPENAI_API_KEY")
        model = "gpt-4o"
        if not api_key:
            raise ValueError("OPENAI_API_KEY not set in environment.")
        return ChatOpenAI(
            model=model,
            temperature=0,
            stream_usage=True,
            callbacks=callbacks or [],
            openai_api_key=api_key,
        )
    elif provider == "gemini":
        api_key = os.getenv("GEMINI_API_KEY")
        model = "gemini-2.0-flash-exp"
        if not api_key:
            raise ValueError("GEMINI_API_KEY not set in environment.")
        return ChatGoogleGenerativeAI(
            model=model,
            temperature=0,
            google_api_key=api_key,
            callbacks=callbacks or [],
        )
    else:
        raise ValueError(f"Unsupported LLM_PROVIDER: {provider}. Use 'openai' or 'gemini'.")
