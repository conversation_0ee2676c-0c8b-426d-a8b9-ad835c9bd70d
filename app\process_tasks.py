
from src.solar_automation import generate_solar_automation_report
from src.wind_automation import generate_wind_automation_report
from src.both_plants_automation import combined_both_plants
from helper.logger_setup import setup_logger
from whatsapp.sender_whatsapp import *
from DB.db_ops import get_solar_report_data, get_wind_report_data, get_combine_report_data, get_combine_report_data_solar
from helper.utils import get_contact_number_from_csv

logging = setup_logger('process_tasks', 'process_tasks.log')

###################################################################################################################
def send_whatsapp_report_solar(plant: str, customer: str, yesterday: str) -> None:
    """
    Sends the solar DGR report via WhatsApp to a list of recipients.

    Args:
        plant (str): Plant short name.
        customer (str): Customer name.
        yesterday (str): Date string for the report.

    Returns:
        None
    """
    try:
        report_data = get_solar_report_data(plant, yesterday)

        # Resolve correct values based on edit action
        if report_data.get("edit_action"):
            poa = report_data.get("edit_poa") if report_data.get("edit_poa") is not None else report_data.get("poa")
            pr = report_data.get("edit_pr") if report_data.get("edit_pr") is not None else report_data.get("pr")
            generation = report_data.get("edit_generation") if report_data.get("edit_generation") is not None else report_data.get("generation")
        else:
            poa = report_data.get("poa")
            pr = report_data.get("pr")
            generation = report_data.get("generation")

        dgr_path = report_data.get("dgr_path")

        # ⚙️ Keep the original name for template but use mapped one for contact lookup
        plant_short_name = plant
        lookup_plant = "IN.INTE.SKRT" if plant == "IN.INTE.SKRT&IN.INTE.SKRT2" else plant

        # Fetch numbers using mapped lookup name
        numbers = get_contact_number_from_csv(lookup_plant)

        # Convert POA if needed
        try:
            poa = round(float(poa) / 1000, 2) if poa is not None else None
        except (ValueError, TypeError):
            pass

        if not numbers:
            logging.warning(f"No WhatsApp contact numbers found for plant: {lookup_plant}")

        # Send message to all recipients
        for number in numbers:
            try:
                send_solar_dgr_template(
                    to_number="91" + number,
                    customer_name=customer,
                    date=yesterday,
                    today_gen=generation,
                    today_pr=pr,
                    today_poa=poa,
                    plant_short_name=plant_short_name,  # original name
                    dgr_path=dgr_path
                )
            except Exception as e:
                logging.error(f"Failed to send report to {number}: {e}", exc_info=True)

    except Exception as e:
        logging.error(f"Error during WhatsApp report sending: {e}", exc_info=True)


def process_plant_solar(plant: str, customer: str, yesterday: str, report_type: str) -> None:
    """
    Processes and generates a solar report for a given plant and customer.

    Args:
        plant (str): Plant short name.
        customer (str): Customer name.
        yesterday (str): Date string for the report.
        report_type (str): Type of report to generate.

    Returns:
        None
    """
    try:
        logging.info(f"Processing report for {plant} - {customer}")
        report_filename = generate_solar_automation_report(plant, yesterday, customer, report_type)
        logging.info(f"Generated report: {report_filename}")
    except Exception as e:
        logging.error(f"Error processing report for {plant}: {e}", exc_info=True)

#################################################################################################################


def send_whatsapp_report_wind(plant: str, customer: str, yesterday: str) -> None:
    """
    Sends the wind DGR report via WhatsApp to a list of recipients.

    Args:
        plant (str): Plant short name.
        customer (str): Customer name.
        yesterday (str): Date string for the report.

    Returns:
        None
    """
    try:
        report_data = get_wind_report_data(plant, yesterday)
        # Use edited values if edit_action is True and the edited value is not None, else use normal value
        if report_data.get("edit_action"):
            wind_speed = report_data.get("edit_wind_speed") if report_data.get("edit_wind_speed") is not None else report_data.get("wind_speed")
            generation = report_data.get("edit_generation") if report_data.get("edit_generation") is not None else report_data.get("generation")
            dgr_path = report_data.get("dgr_path")
        else:
            wind_speed = report_data.get("wind_speed")
            generation = report_data.get("generation")
            dgr_path = report_data.get("dgr_path")
        numbers = get_contact_number_from_csv(plant)
        if not numbers:
            logging.warning(f"No WhatsApp contact numbers found for plant: {plant}")

        for number in numbers:
            try:
                send_wind_dgr_template(
                    to_number="91" + number,
                    customer_name=customer,
                    today_gen=generation,
                    today_ws=wind_speed,
                    report_date=yesterday,
                    plant_short_name=plant,
                    dgr_path=dgr_path
                )
            except Exception as e:
                logging.error(f"Failed to send report to {number}: {e}", exc_info=True)
    except Exception as e:
        logging.error(f"Error during WhatsApp report sending: {e}", exc_info=True)


def process_plant_wind(plant: str, customer: str, yesterday: str, report_type: str) -> None:
    """
    Processes and generates a wind report for a given plant and customer.

    Args:
        plant (str): Plant short name.
        customer (str): Customer name.
        yesterday (str): Date string for the report.
        report_type (str): Type of report to generate.

    Returns:
        None
    """
    try:
        logging.info(f"Processing report for {plant} - {customer}")
        report_filename = generate_wind_automation_report(plant, yesterday, customer, report_type, "0")
        logging.info(f"Generated report: {report_filename}")
    except Exception as e:
        logging.error(f"Error processing report for {plant}: {e}", exc_info=True)

###################################################################################################################



###################################################################################################################
def process_plant_both(
    plant_solar: str,
    plant_wind: str,
    customer: str,
    yesterday: str,
    report_type: str
) -> None:
    """
    Processes and generates a combined solar and wind report for given plants and customer.

    Args:
        plant_solar (str): Solar plant short name.
        plant_wind (str): Wind plant short name.
        customer (str): Customer name.
        yesterday (str): Date string for the report.
        report_type (str): Type of report to generate.

    Returns:
        None
    """
    try:
        logging.info(f"Processing report for {plant_solar} & {plant_wind} - {customer}")
        report_filename = combined_both_plants(plant_solar, plant_wind, yesterday, customer, report_type, "0")
        logging.info(f"Generated report: {report_filename}")
    except Exception as e:
        logging.error(f"Error processing report for {plant_solar} & {plant_wind}: {e}", exc_info=True)

# def send_whatsapp_report_both(
#     plant_solar: str,
#     plant_wind: str,
#     customer: str,
#     yesterday: str
# ) -> None:
#     """
#     Sends the combined solar and wind DGR report via WhatsApp to a list of recipients.

#     Args:
#         plant_solar (str): Solar plant short name.
#         plant_wind (str): Wind plant short name.
#         customer (str): Customer name.
#         yesterday (str): Date string for the report.

#     Returns:
#         None
#     """
#     try:
#         # 🌐 Define plant mappings for contact lookup
#         solar_lookup_map = {
#             "IN.INTE.BAL1&IN.INTE.BAL2": "IN.INTE.BAL1",
#             "IN.INTE.JOD1&IN.INTE.JOD2": "IN.INTE.JOD1",
#         }
#         wind_lookup_map = {
#             "IN.INTE.SKRT&IN.INTE.SKRT2": "IN.INTE.SKRT",
#         }

#         # Keep original names for messages
#         plant_solar_short = plant_solar
#         plant_wind_short = plant_wind

#         # Use mapped names for fetching numbers and data
#         lookup_solar = solar_lookup_map.get(plant_solar, plant_solar)
#         lookup_wind = wind_lookup_map.get(plant_wind, plant_wind)

#         # 🧩 Get combined report data
#         report_data = get_combine_report_data(lookup_wind, yesterday)
#         if plant_solar in solar_lookup_map:
#             from DB.db_ops import get_combine_report_data_solar
#             report_data = get_combine_report_data_solar(lookup_solar, yesterday)

#         # Use edited values if edit_action is True
#         if report_data.get("edit_action"):
#             wind_speed = report_data.get("edit_wind_speed") or report_data.get("wind_speed")
#             generation_wind = report_data.get("edit_generation_wind") or report_data.get("generation_wind")
#             generation_solar = report_data.get("edit_generation_solar") or report_data.get("generation_solar")
#             pr = report_data.get("edit_pr") or report_data.get("pr")
#             poa = report_data.get("edit_poa") or report_data.get("poa")
#         else:
#             wind_speed = report_data.get("wind_speed")
#             generation_wind = report_data.get("generation_wind")
#             generation_solar = report_data.get("generation_solar")
#             pr = report_data.get("pr")
#             poa = report_data.get("poa")

#         dgr_path = report_data.get("dgr_path")

#         # 🧮 Calculate totals
#         total_calculated = round(float(generation_solar) + float(generation_wind), 2)

#         # 🔢 Get WhatsApp contact numbers using lookup plant
#         numbers = get_contact_number_from_csv(lookup_wind)

#         # 🧠 Convert POA safely
#         try:
#             poa = round(float(poa) / 1000, 2) if poa is not None else None
#         except (ValueError, TypeError):
#             pass

#         if not numbers or numbers == ["N/A"]:
#             logging.warning(f"No WhatsApp contact numbers found for plant: {lookup_wind}")
#             return

#         # 🚀 Send messages
#         for number_item in numbers:
#             if number_item == "N/A":
#                 logging.warning(f"Skipping 'N/A' contact for plant: {lookup_wind}")
#                 continue

#             try:
#                 send_both_dgr_template(
#                     to_number="91" + number_item,
#                     customer_name=customer,
#                     wind_today_gen=generation_wind,
#                     wind_today_ws=wind_speed,
#                     report_date=yesterday,
#                     solar_today_gen=generation_solar,
#                     solar_today_pr=pr,
#                     solar_today_poa=poa,
#                     dgr_path=dgr_path,
#                     plant_short_name_wind=plant_wind_short,  # Original name preserved
#                     total_calculated=total_calculated
#                 )
#             except Exception as e:
#                 logging.error(f"Failed to send report to {number_item}: {e}", exc_info=True)

#     except Exception as e:
#         logging.error(f"Error during WhatsApp report sending for both plants: {e}", exc_info=True)



def send_whatsapp_report_both(
    plant_solar: str,
    plant_wind: str,
    customer: str,
    yesterday: str
) -> None:
    """
    Sends the combined solar and wind DGR report via WhatsApp to a list of recipients.

    Args:
        plant_solar (str): Solar plant short name.
        plant_wind (str): Wind plant short name.
        customer (str): Customer name.
        yesterday (str): Date string for the report.

    Returns:
        None
    """
    try:

        report_data = get_combine_report_data_solar(plant_solar, yesterday)
        if plant_solar == "IN.INTE.BAL1&IN.INTE.BAL2" or plant_solar == "IN.INTE.JOD1&IN.INTE.JOD2":
            report_data = get_combine_report_data_solar(plant_solar, yesterday)

        # Use edited values if edit_action is True and the edited value is not None, else use normal value
        if report_data.get("edit_action"):
            wind_speed = report_data.get("edit_wind_speed") if report_data.get("edit_wind_speed") is not None else report_data.get("wind_speed")
            generation_wind = report_data.get("edit_generation_wind") if report_data.get("edit_generation_wind") is not None else report_data.get("generation_wind")
            generation_solar = report_data.get("edit_generation_solar") if report_data.get("edit_generation_solar") is not None else report_data.get("generation_solar")
            pr = report_data.get("edit_pr") if report_data.get("edit_pr") is not None else report_data.get("pr")
            poa = report_data.get("edit_poa") if report_data.get("edit_poa") is not None else report_data.get("poa")
            dgr_path = report_data.get("dgr_path")
        else:
            wind_speed = report_data.get("wind_speed")
            generation_wind = report_data.get("generation_wind")
            generation_solar = report_data.get("generation_solar")
            pr = report_data.get("pr")
            poa = report_data.get("poa")
            dgr_path = report_data.get("dgr_path")
        total_calculated = round(float(generation_solar) + float(generation_wind), 2)


        numbers = get_contact_number_from_csv(plant_wind)


            
        try:
            poa = round(float(poa) / 1000, 2) if poa is not None else None
        except (ValueError, TypeError):
            poa = poa
        if not numbers or numbers == ["N/A"]:
            logging.warning(f"No WhatsApp contact numbers found for plant: {plant_wind}")
        else:
            for number_item in numbers:
                if number_item == "N/A":
                    logging.warning(f"Skipping 'N/A' contact for plant: {plant_wind}")
                    continue
                try:
                    send_both_dgr_template(
                        to_number="91" + number_item,
                        customer_name=customer,
                        wind_today_gen=generation_wind,
                        wind_today_ws=wind_speed,
                        report_date=yesterday,
                        solar_today_gen=generation_solar,
                        solar_today_pr=pr,
                        solar_today_poa=poa,
                        dgr_path=dgr_path,
                        plant_short_name_wind=plant_solar,
                        total_calculated=total_calculated
                    )
                except Exception as e:
                    logging.error(f"Failed to send report to {number_item}: {e}", exc_info=True)
    except Exception as e:
        logging.error(f"Error during WhatsApp report sending for both plants: {e}", exc_info=True)



###################################################################################################################
