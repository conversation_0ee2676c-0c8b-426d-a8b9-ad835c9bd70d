# import sys
# from getpass import getpass
# from DB.setup_db import session as db_session
# from DB.models import AdminUser
# from werkzeug.security import generate_password_hash

# def main():
#     print("Add a new admin user to the admin_users table")
#     username = input("Admin Username: ").strip()
#     email = input("Admin Email: ").strip()
#     password = input("Admin Password: ").strip()
#     if not username or not email or not password:
#         print("All fields are required.")
#         sys.exit(1)
#     db = db_session()
#     try:
#         if db.query(AdminUser).filter((AdminUser.username == username) | (AdminUser.email == email)).first():
#             print("An admin with this username or email already exists.")
#             sys.exit(1)
#         password_hash = generate_password_hash(password)
#         admin = AdminUser(username=username, email=email, password_hash=password_hash)
#         db.add(admin)
#         db.commit()
#         print(f"Admin user '{username}' added successfully.")
#     except Exception as e:
#         db.rollback()
#         print("Error adding admin user:", e)
#     finally:
#         db_session.remove()

# if __name__ == "__main__":
#     main()




import sys
from getpass import getpass
from DB.setup_db import session as db_session
from DB.models import AdminUser
from werkzeug.security import generate_password_hash

def main():
    print("Add a new admin user to the admin_users table")
    username = "Harikrishnan"
    email = '<EMAIL>'
    password = "Hari@logos"
    if not username or not email or not password:
        print("All fields are required.")
        sys.exit(1)
    db = db_session()
    try:
        if db.query(AdminUser).filter((AdminUser.username == username) | (AdminUser.email == email)).first():
            print("An admin with this username or email already exists.")
            sys.exit(1)
        password_hash = generate_password_hash(password)
        admin = AdminUser(username=username, email=email, password_hash=password_hash)
        db.add(admin)
        db.commit()
        print(f"Admin user '{username}' added successfully.")
    except Exception as e:
        db.rollback()
        print("Error adding admin user:", e)
    finally:
        db_session.remove()

if __name__ == "__main__":
    print('started')
    main()
    print('ended')
