---
description: Repository Information Overview
alwaysApply: true
---

# Wind & Solar Automation Platform Information

## Summary
A modular, production-ready platform for automating daily generation reports (DGR) for wind and solar plants. The system integrates with WhatsApp for report delivery and supports advanced data processing, plotting, and PDF generation. It provides automated generation of daily, monthly, and yearly reports with professional formatting.

## Structure
- **app/**: Web routes, frontend handlers, scheduling, and webhook endpoints
- **DB/**: SQLAlchemy models and database operations
- **helper/**: Utilities for plotting, PDF/CSV generation, logging, S3, and integrations
- **src/**: Core automation for wind, solar, and combined plants
- **whatsapp/**: WhatsApp message sending and extraction logic
- **config/**: Centralized configuration and environment variable loading
- **static/**: Customer data, logos, generated reports, and plots
- **templates/**: HTML templates for the web interface
- **logs/**: Application logs for various components

## Language & Runtime
**Language**: Python
**Version**: 3.12
**Framework**: Flask 3.1.0
**Database**: SQLAlchemy with MySQL (PyMySQL)

## Dependencies
**Main Dependencies**:
- Flask==3.1.0 - Web framework
- reportlab==4.3.1 - PDF generation
- PyPDF2==3.0.1 - PDF manipulation
- pandas==2.2.3 - Data processing
- matplotlib==3.10.0 - Data visualization
- schedule==1.2.2 - Task scheduling
- boto3 - AWS S3 integration
- sqlalchemy - ORM for database operations
- pymysql - MySQL connector

**Development Dependencies**:
- python-dotenv==1.0.1 - Environment variable management

## Build & Installation
```bash
# Create and activate virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Set up environment variables
# Copy .env.example to .env and fill in required values
```

## Configuration
**Environment Variables**:
- VERIFY_TOKEN - Token for webhook verification
- PUBLIC_URL - Publicly accessible URL for webhooks
- DEBUG - Development mode flag
- CUSTOMER_DATA_CSV_PATH - Path to customer data CSV
- API_TOKEN - Token for external integrations

**Configuration Files**:
- config/settings.py - Main application settings
- config/creds_storage_s3.py - AWS S3 credentials

## Main Files
**Entry Point**: main.py
**Application Factory**: app/__init__.py
**Database Models**: DB/models.py (WindReport, SolarReport, DgrBothDb, WhatsAppMessage)
**Core Logic**: 
- src/wind_automation.py
- src/solar_automation.py
- src/both_plants_automation.py

## Data Storage
**Database**: MySQL with SQLAlchemy ORM
**File Storage**: Local filesystem and AWS S3
**Models**:
- WindReport - Wind plant generation data
- SolarReport - Solar plant generation data
- DgrBothDb - Combined wind and solar plant data
- WhatsAppMessage - Message tracking for WhatsApp integration

## Web Interface
**Routes**: app/routes.py
**Templates**: templates/index.html, templates/login.html
**Static Assets**: static/ (includes generated reports and plots)

## Deployment
**Server**: Flask development server (production should use WSGI)
**Host**: 0.0.0.0
**Port**: 5000
**Background Processing**: Threading for scheduled tasks