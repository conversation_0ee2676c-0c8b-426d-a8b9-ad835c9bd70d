<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <title>DGR Data Upload</title>
  <!-- Bootstrap 5 CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

  <style>
    .dgr-upload-form-group {
      margin-bottom: 1.2rem;
    }

    .dgr-upload-label {
      font-weight: 500;
      margin-bottom: 0.4rem;
      display: block;
    }

    .dgr-upload-input {
      width: 100%;
      padding: 0.5rem;
      border-radius: 0.375rem;
      border: 1px solid #ced4da;
      font-size: 1rem;
      box-sizing: border-box;
    }

    .dgr-upload-btn {
      background: #198754;
      color: #fff;
      border: none;
      border-radius: 0.375rem;
      padding: 0.5rem 1.2rem;
      font-weight: 600;
      cursor: pointer;
      margin-right: 0.5rem;
      transition: background 0.15s;
    }

    .dgr-upload-btn:disabled {
      opacity: 0.7;
      cursor: not-allowed;
    }

    .dgr-upload-btn-cancel {
      background: #6c757d;
      color: #fff;
    }

    .dgr-upload-info {
      font-size: 0.97rem;
      color: #495057;
      margin-bottom: 1rem;
      background: #f8f9fa;
      border-left: 4px solid #0dcaf0;
      padding: 0.7rem 1rem;
      border-radius: 0.3rem;
    }
  </style>
</head>

<body>
  <div class="container py-4">
    <form id="dgrDataUploadForm" enctype="multipart/form-data" autocomplete="off" method="POST"
      action="/dgr_data_upload">

      <noscript>
        <div class="alert alert-warning">
          <b>JavaScript is required to upload files.</b><br> Please enable JavaScript in your browser and reload the
          page.
        </div>
      </noscript>

      <div class="dgr-upload-info">
        <b>Instructions:</b> Upload an Excel file (.xlsx) with three sheets named <b>Wind</b>, <b>Solar</b>, and
        <b>Both</b>.
        <br> Each sheet should follow the required column structure for its data type.
      </div>

      <div class="dgr-upload-form-group">
        <label class="dgr-upload-label" for="dgr_excel_file">Select Excel File (.xlsx):</label>
        <input class="dgr-upload-input" type="file" id="dgr_excel_file" name="excel_file" accept=".xlsx" required />
      </div>

      <!-- Inline message container -->
      <div id="dgrUploadMessage" class="mb-3" style="display:none;"></div>

      <div style="text-align:right;">
        <button type="submit" class="dgr-upload-btn" id="dgrUploadSubmitBtn">
          <i class="fas fa-upload me-1"></i>Upload
        </button>
        <button type="button" class="dgr-upload-btn dgr-upload-btn-cancel"
          onclick="window.closeDgrModal && window.closeDgrModal()"> Cancel
        </button>
      </div>
    </form>
  </div>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      const form = document.getElementById('dgrDataUploadForm');
      const fileInput = document.getElementById('dgr_excel_file');
      const submitBtn = document.getElementById('dgrUploadSubmitBtn');
      const messageDiv = document.getElementById('dgrUploadMessage');

      form.onsubmit = function (e) {
        e.preventDefault();

        // Reset message
        messageDiv.style.display = 'none';
        messageDiv.className = "";
        messageDiv.textContent = "";

        if (!fileInput.files.length) {
          showMessage("Please select an Excel file to upload.", "danger");
          return;
        }

        const file = fileInput.files[0];
        if (!file.name.endsWith('.xlsx')) {
          showMessage("Only .xlsx files are supported.", "danger");
          return;
        }

        const formData = new FormData();
        formData.append('excel_file', file);

        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Uploading...';

        fetch('/dgr_data_upload', { method: 'POST', body: formData })
          .then(response => response.json())
          .then(data => {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-upload me-1"></i>Upload';

            if (data.success) {
              showMessage(data.message || "Upload successful!", "success");
              setTimeout(() => {
                if (window.closeDgrModal) window.closeDgrModal();
                if (window.$ && $('#filterBtn').length) $('#filterBtn').trigger('click');
              }, 1500);
            } else {
              showMessage("Upload failed: " + (data.error || "Unknown error"), "danger");
            }
          })
          .catch(err => {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-upload me-1"></i>Upload';
            showMessage("An error occurred during upload. Please try again.", "danger");
          });
      };

      function showMessage(message, type) {
        messageDiv.className = "alert alert-" + type;
        messageDiv.textContent = message;
        messageDiv.style.display = "block";
      }
    });
  </script>
</body>

</html>
