import setuptools

with open("README.md", "r", encoding="utf-8") as f:
    long_description = f.read()

with open("requirements.txt", "r", encoding="utf-8") as f:
    requirements = [line.strip() for line in f if line.strip() and not line.startswith("#")]

__version__ = "0.0.0"

REPO_NAME = "DGR-Generation"
AUTHOR_USER_NAME = "<PERSON><PERSON><PERSON><PERSON><PERSON>"
AUTHOR_EMAIL = "<PERSON><PERSON><PERSON><PERSON><PERSON>@logos-labs.com"

setuptools.setup(
    name=REPO_NAME,
    version=__version__,
    author=AUTHOR_USER_NAME,
    author_email=AUTHOR_EMAIL,
    description="Automated DGR (Daily Generation Report) generation and management system.",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url=f"https://github.com/Logos-Labs-India/{REPO_NAME}",
    project_urls={
        "Bug Tracker": f"https://github.com/Logos-Labs-India/{REPO_NAME}/issues",
    },
    package_dir={"": ""},
    packages=setuptools.find_packages(where=""),
    install_requires=requirements,
    include_package_data=True,
    python_requires=">=3.7",
)
