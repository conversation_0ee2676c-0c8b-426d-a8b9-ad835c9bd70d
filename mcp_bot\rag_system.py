# import os
# import sys
# sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
# import sys
# import pickle
# import os
# import re
# from typing import List, Optional, Dict, Any
# import numpy as np
# from dotenv import load_dotenv
# from openai import OpenAI
# from rank_bm25 import BM25Okapi
# from langchain.text_splitter import RecursiveCharacterTextSplitter
# from helper.logger_setup import setup_logger
# import tiktoken
# from langchain_google_genai import GoogleGenerativeAIEmbeddings
# import faiss

# # --- NEW: LangSmith tracing & helper ---
# from langsmith import traceable

# load_dotenv()

# os.environ["GOOGLE_API_KEY"] = os.getenv('GEMINI_API_KEY')

# logger = setup_logger("rag_system", "rag_system.log")

# # ---------- Helper utils ----------
# def normalize_text(text: str) -> str:
#     logger.debug(f"Normalizing text of length {len(text)}")
#     return text.replace("–", "-").replace("\xa0", " ").strip()

# def count_tokens(text: str, model: str = "gpt-4o") -> int:
#     enc = tiktoken.encoding_for_model(model)
#     token_count = len(enc.encode(text))
#     logger.debug(f"Counted {token_count} tokens for model {model}")
#     return token_count


# @traceable
# def generate_answer(query: str, context: str, openai_client: OpenAI, model: str = "gpt-4o") -> str:
#     prompt = f"""
# You are an expert AI assistant for Integrum Energy.
# Answer the question using ONLY the context below.
# If the answer is not in the context, politely say "I don't know".

# Context:
# {context}

# Question: {query}
# Answer:
# """
#     logger.info(f"generate_answer called with query: {query[:80]}...")
#     logger.debug(f"Generating answer. Query tokens: {count_tokens(query)}, Context tokens: {count_tokens(context)}")

#     response = openai_client.chat.completions.create(
#         model=model,
#         messages=[
#             {"role": "system", "content": "You are a helpful and professional AI assistant."},
#             {"role": "user", "content": prompt}
#         ]
#     )
#     # defensive access
#     text = ""
#     try:
#         text = response.choices[0].message.content
#         logger.debug("OpenAI response received successfully.")
#     except Exception as e:
#         logger.error(f"Error accessing OpenAI response: {e}")
#         # fallback for response shape differences
#         text = getattr(response.choices[0], "text", "")
#     logger.info(f"Generated answer: {text[:120]}...")
#     return text

# # ---------- RAG retriever class (minor edits only) ----------
# class RAGRetrieverFAISS:
#     def __init__(self, persist_dir: str = "./faiss_index"):
#         self.persist_dir = os.path.abspath(persist_dir)
#         os.makedirs(self.persist_dir, exist_ok=True)
#         self.index_file = os.path.join(self.persist_dir, "index.faiss")
#         self.docs_file = os.path.join(self.persist_dir, "docs.pkl")
#         self.meta_file = os.path.join(self.persist_dir, "meta.pkl")

#         self.docs: List[str] = []
#         self.metadata: List[dict] = []
#         self.embeddings: Optional[np.ndarray] = None
#         self.index = None

#         self.bm25 = None
#         self.tokenized_docs: List[List[str]] = []

#         self.openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
#         # self.embedding_provider = "gemini"
#         self.embedding_provider = "openai"
#         self.gemini_client = GoogleGenerativeAIEmbeddings(model="models/gemini-embedding-001")

#     def tokenize(self, text: str) -> List[str]:
#         tokens = re.findall(r"\w+", text.lower())
#         logger.debug(f"Tokenized text to {len(tokens)} tokens.")
#         return tokens

#     def ingest_file(self, txt_file: str, chunk_size: int = 500, chunk_overlap: int = 50):
#         logger.info(f"ingest_file called with txt_file={txt_file}, chunk_size={chunk_size}, chunk_overlap={chunk_overlap}")
#         txt_file = os.path.abspath(txt_file)
#         if not os.path.exists(txt_file):
#             logger.error(f"File not found: {txt_file}")
#             raise FileNotFoundError(f"File not found: {txt_file}")

#         with open(txt_file, "r", encoding="utf-8") as f:
#             text = normalize_text(f.read())

#         splitter = RecursiveCharacterTextSplitter(chunk_size=chunk_size, chunk_overlap=chunk_overlap)
#         chunks = [normalize_text(d) for d in splitter.split_text(text)]
#         logger.debug(f"Split text into {len(chunks)} chunks.")

#         for i, chunk in enumerate(chunks):
#             self.docs.append(chunk)
#             self.metadata.append({
#                 "source": txt_file,
#                 "chunk_id": f"{os.path.basename(txt_file)}_chunk_{i}"
#             })
#             self.tokenized_docs.append(self.tokenize(chunk))

#         self.bm25 = BM25Okapi(self.tokenized_docs)
#         logger.info(f"Ingested {len(chunks)} chunks from {txt_file}")

#     def get_embedding(self, text: str) -> np.ndarray:
#         logger.debug(f"get_embedding called with provider={self.embedding_provider}, text length={len(text)}")
#         if self.embedding_provider == "openai":
#             response = self.openai_client.embeddings.create(model="text-embedding-3-small", input=text)
#             logger.debug("OpenAI embedding created successfully.")
#             return np.array(response.data[0].embedding, dtype=np.float32)
#         elif self.embedding_provider == "gemini":
#             emb = self.gemini_client.embed_query(text)
#             logger.debug("Gemini embedding created successfully.")
#             return np.array(emb, dtype=np.float32)
#         else:
#             logger.error(f"Unsupported embedding provider: {self.embedding_provider}")
#             raise ValueError(f"Unsupported embedding provider: {self.embedding_provider}")

#     def build_or_load_index(self):
#         logger.info("build_or_load_index called.")
#         if os.path.exists(self.index_file) and os.path.exists(self.docs_file) and os.path.exists(self.meta_file):
            
#             logger.debug("Loading existing FAISS index and document files.")
#             self.index = faiss.read_index(self.index_file)
#             with open(self.docs_file, "rb") as f:
#                 self.docs = pickle.load(f)
#             with open(self.meta_file, "rb") as f:
#                 self.metadata = pickle.load(f)
#             self.tokenized_docs = [self.tokenize(d) for d in self.docs]
#             self.bm25 = BM25Okapi(self.tokenized_docs)
#             logger.info(f"Loaded existing FAISS index with {len(self.docs)} documents.")
#         else:
            
#             logger.debug("Creating new FAISS index from documents.")
#             self.embeddings = np.array([self.get_embedding(d) for d in self.docs], dtype=np.float32)
#             dim = self.embeddings.shape[1]
#             self.index = faiss.IndexIDMap(faiss.IndexFlatL2(dim))
#             ids = np.arange(len(self.docs))
#             self.index.add_with_ids(self.embeddings, ids)
#             faiss.write_index(self.index, self.index_file)
#             with open(self.docs_file, "wb") as f:
#                 pickle.dump(self.docs, f)
#             with open(self.meta_file, "wb") as f:
#                 pickle.dump(self.metadata, f)
#             logger.info(f"Created new FAISS index with {len(self.docs)} documents.")

#     def add_documents_incremental(self, new_docs: List[str], source_name: str):
#         logger.info(f"add_documents_incremental called with {len(new_docs)} new docs, source={source_name}")
#         new_embeddings = np.array([self.get_embedding(d) for d in new_docs], dtype=np.float32)
#         start_id = len(self.docs)
#         ids = np.arange(start_id, start_id + len(new_docs))
#         self.index.add_with_ids(new_embeddings, ids)

#         for i, doc in enumerate(new_docs):
#             self.docs.append(doc)
#             self.metadata.append({"source": source_name, "chunk_id": f"{source_name}_chunk_{start_id+i}"})
#             self.tokenized_docs.append(self.tokenize(doc))

#         self.bm25 = BM25Okapi(self.tokenized_docs)

       
#         faiss.write_index(self.index, self.index_file)
#         with open(self.docs_file, "wb") as f:
#             pickle.dump(self.docs, f)
#         with open(self.meta_file, "wb") as f:
#             pickle.dump(self.metadata, f)
#         logger.info(f"Added {len(new_docs)} new documents incrementally.")

#     def delete_document(self, doc_id: int):
#         logger.info(f"delete_document called with doc_id={doc_id}")
#         if doc_id < 0 or doc_id >= len(self.docs):
#             logger.error(f"Invalid document ID: {doc_id}")
#             return
#         self.docs.pop(doc_id)
#         self.metadata.pop(doc_id)
#         self.tokenized_docs.pop(doc_id)
#         self.bm25 = BM25Okapi(self.tokenized_docs) if self.docs else None

#         if self.docs:
#             self.embeddings = np.array([self.get_embedding(d) for d in self.docs], dtype=np.float32)
#             dim = self.embeddings.shape[1]
#             import faiss
#             self.index = faiss.IndexIDMap(faiss.IndexFlatL2(dim))
#             ids = np.arange(len(self.docs))
#             self.index.add_with_ids(self.embeddings, ids)
#             logger.debug("FAISS index rebuilt after document deletion.")
#         else:
#             self.index = None
#             logger.debug("All documents deleted; FAISS index set to None.")

        
#         faiss.write_index(self.index, self.index_file)
#         with open(self.docs_file, "wb") as f:
#             pickle.dump(self.docs, f)
#         with open(self.meta_file, "wb") as f:
#             pickle.dump(self.metadata, f)
#         logger.info(f"Document ID {doc_id} deleted and index updated.")

#     # -------------------- retrieval (traced) --------------------
#     @traceable
#     def retrieve(self, query: str, k: int = 5, mode: str = "hybrid") -> List[str]:
#         logger.info(f"retrieve called with query='{query[:80]}...', k={k}, mode={mode}")
#         results = []
#         faiss_docs = []
#         if self.index:
#             qemb = self.get_embedding(query).reshape(1, -1)
#             distances, indices = self.index.search(qemb, k)
#             logger.debug(f"FAISS search returned indices: {indices[0]}, distances: {distances[0]}")
#             # defend if fewer results
#             for j, i in enumerate(indices[0]):
#                 if i < len(self.docs) and i != -1:
#                     faiss_docs.append((self.docs[i], float(distances[0][j])))

#         bm25_docs = []
#         if self.bm25:
#             tokenized_query = self.tokenize(query)
#             scores = self.bm25.get_scores(tokenized_query)
#             top_indices = np.argsort(scores)[::-1][:k]
#             logger.debug(f"BM25 top indices: {top_indices}")
#             for idx in top_indices:
#                 bm25_docs.append((self.docs[idx], float(scores[idx])))

#         if mode == "faiss":
#             results = [doc for doc, _ in faiss_docs]
#         elif mode == "bm25":
#             results = [doc for doc, _ in bm25_docs]
#         elif mode == "hybrid":
#             faiss_scores = np.array([1/(1+d) for _, d in faiss_docs]) if faiss_docs else np.array([])
#             bm25_scores  = np.array([s for _, s in bm25_docs]) if bm25_docs else np.array([])
#             if faiss_scores.size:
#                 faiss_scores = (faiss_scores - faiss_scores.min()) / (np.ptp(faiss_scores) + 1e-8)
#             if bm25_scores.size:
#                 bm25_scores = (bm25_scores - bm25_scores.min()) / (np.ptp(bm25_scores) + 1e-8)
#             combined = {}
#             for (doc, _), f in zip(faiss_docs, faiss_scores):
#                 combined[doc] = combined.get(doc, 0) + 0.5*f
#             for (doc, _), b in zip(bm25_docs, bm25_scores):
#                 combined[doc] = combined.get(doc, 0) + 0.5*b
#             results = [doc for doc, _ in sorted(combined.items(), key=lambda x: x[1], reverse=True)][:k]

#         logger.info(f"retrieve returning {len(results)} results.")
#         if not results:
#             logger.warning("No relevant documents found for the query.")
#         return results

#     def assemble_context(self, docs: List[str], max_tokens: int = 2500) -> str:
#         logger.info(f"assemble_context called with {len(docs)} docs, max_tokens={max_tokens}")
#         context = ""
#         tokens = 0
#         for doc in docs:
#             t_count = count_tokens(doc)
#             if tokens + t_count > max_tokens:
#                 logger.debug(f"Context token limit reached at {tokens} tokens.")
#                 break
#             context += doc + "\n"
#             tokens += t_count
#         logger.debug(f"assemble_context returning context with {tokens} tokens.")
#         return context.strip()

#     # document helpers are unchanged
#     def list_documents(self, limit: int = 20):
#         logger.info(f"Total documents: {len(self.docs)}")
#         for i, meta in enumerate(self.metadata[:limit]):
#             logger.info(f"ID: {i}, Source: {meta['source']}, Chunk ID: {meta['chunk_id']}")

#     def view_document(self, doc_id: int):
#         if doc_id < 0 or doc_id >= len(self.docs):
#             logger.error(f"Invalid document ID: {doc_id}")
#             return
#         logger.info(f"Document ID: {doc_id}")
#         logger.info(f"Source: {self.metadata[doc_id]['source']}")
#         logger.info(f"Chunk ID: {self.metadata[doc_id]['chunk_id']}")
#         logger.info("Content:")
#         logger.info(self.docs[doc_id])

# # ------------------------- EVALUATION UTILITIES -------------------------
# def compute_recall_at_k(retrieved_docs: List[List[int]], ground_truths: List[List[int]], k: int = 5) -> float:
#     """
#     retrieved_docs: list of list of doc_ids returned for each query (ordered)
#     ground_truths: list of lists of relevant doc_ids for each query
#     """
#     logger.info(f"compute_recall_at_k called with k={k}, {len(retrieved_docs)} queries.")
#     n = len(ground_truths)
#     hits = 0
#     for r, gt in zip(retrieved_docs, ground_truths):
#         topk = r[:k]
#         if any(d in gt for d in topk):
#             hits += 1
#     recall = hits / n if n else 0.0
#     logger.info(f"Recall@{k}: {recall}")
#     return recall

# def compute_mrr(retrieved_docs: List[List[int]], ground_truths: List[List[int]]) -> float:
#     logger.info(f"compute_mrr called for {len(retrieved_docs)} queries.")
#     n = len(ground_truths)
#     rr_total = 0.0
#     for r, gt in zip(retrieved_docs, ground_truths):
#         rr = 0.0
#         for rank, doc_id in enumerate(r, start=1):
#             if doc_id in gt:
#                 rr = 1.0 / rank
#                 break
#         rr_total += rr
#     mrr = rr_total / n if n else 0.0
#     logger.info(f"MRR: {mrr}")
#     return mrr

# @traceable
# def faithfulness_check(answer: str, context: str, openai_client: OpenAI, model: str = "gpt-4o") -> Dict[str, Any]:
#     """
#     Ask the LLM to judge whether `answer` is fully supported by `context`.
#     Returns structured response with judgement and reason.
#     """
#     logger.info("faithfulness_check called.")
#     check_prompt = f"""
# You are an evaluator. Given a CONTEXT and an ANSWER, reply in JSON with two keys:
# - supported: "yes" or "no" (is the ANSWER fully supported by the CONTEXT?)
# - reason: short explanation (one or two sentences)

# CONTEXT:
# {context}

# ANSWER:
# {answer}
# """
#     resp = openai_client.chat.completions.create(
#         model=model,
#         messages=[
#             {"role": "system", "content": "You are a concise evaluator."},
#             {"role": "user", "content": check_prompt}
#         ],
#         temperature=0.0
#     )
#     text = resp.choices[0].message.content
#     logger.debug(f"faithfulness_check LLM response: {text}")
#     # Try best-effort parse of JSON-like output; fallback to raw string in reason
#     supported = "no"
#     reason = text.strip()
#     if '"supported"' in text.lower() or '"reason"' in text.lower():
#         try:
#             import json
#             # naive attempt to extract JSON object from model text
#             json_text = text[text.find("{"):text.rfind("}")+1]
#             parsed = json.loads(json_text)
#             supported = parsed.get("supported", "no")
#             reason = parsed.get("reason", reason)
#         except Exception as e:
#             logger.error(f"Error parsing faithfulness_check JSON: {e}")
#             # leave defaults
#             pass
#     logger.info(f"faithfulness_check result: supported={supported}, reason={reason[:80]}...")
#     return {"supported": supported, "reason": reason, "raw": text}

# # --------------------- Example evaluation run ---------------------
# def run_evaluation_example(rag: RAGRetrieverFAISS, testset: List[Dict[str, Any]]):
#     """
#     testset: list of dicts:
#       {
#         "query": "...",
#         "ground_truth_doc_ids": [0, 2],   # indices into rag.docs that are correct
#       }
#     """
#     logger.info(f"run_evaluation_example called with {len(testset)} test cases.")
#     retrieved_doc_id_lists = []
#     gt_lists = []
#     faithfulness_results = []

#     for t in testset:
#         q = t["query"]
#         gt = t["ground_truth_doc_ids"]
#         logger.info(f"Evaluating query: {q}")
#         # retrieve returns docs (content) — we must map them to doc indices by matching content
#         docs = rag.retrieve(q, k=10, mode="hybrid")
#         # map each retrieved doc to its index in rag.docs (first match)
#         retrieved_ids = []
#         for d in docs:
#             try:
#                 idx = rag.docs.index(d)
#             except ValueError:
#                 idx = -1
#             retrieved_ids.append(idx)
#         logger.debug(f"Retrieved doc indices: {retrieved_ids}, ground truth: {gt}")
#         retrieved_doc_id_lists.append(retrieved_ids)
#         gt_lists.append(gt)

#         # assemble context from top-k (use k=5 here)
#         ctx = rag.assemble_context(docs[:5])
#         # generate answer
#         ans = generate_answer(q, ctx, rag.openai_client)
#         # faithfulness check
#         faith = faithfulness_check(ans, ctx, rag.openai_client)
#         faithfulness_results.append({"query": q, "answer": ans, "faith": faith})

#     recall5 = compute_recall_at_k(retrieved_doc_id_lists, gt_lists, k=5)
#     mrr = compute_mrr(retrieved_doc_id_lists, gt_lists)

#     # Log and return structured metrics
#     metrics = {
#         "recall@5": recall5,
#         "mrr": mrr,
#         "num_examples": len(testset),
#         "faithfulness_samples": faithfulness_results[:20]  # don't bloat logs
#     }
#     logger.info(f"Evaluation metrics: {metrics}")
#     return metrics

# # --------------------- main (example) ---------------------
# if __name__ == "__main__":
#     rag = RAGRetrieverFAISS()

#     # Ingest initial txt file
#     txt_path = os.path.join("mcp_bot", "integrum_energy.txt")
#     rag.ingest_file(txt_path)

#     # Build or load FAISS index
#     rag.build_or_load_index()

#     # Example interactive query
#     query = "Who is the COO of Integrum Energy?"
#     top_docs = rag.retrieve(query, k=5, mode="hybrid")
#     context = rag.assemble_context(top_docs)

#     if top_docs:
#         answer = generate_answer(query, context, rag.openai_client)
#         logger.info(f"\n[ANSWER]: {answer}")
#     else:
#         logger.info("\n[ANSWER]: I don't know")

#     # ---------- Example evaluation run (you should replace with your labeled testset) ----------
#     # testset example: list of maps with 'query' and 'ground_truth_doc_ids' (indices into rag.docs)
#     example_testset = [
#         {"query": "Who is the COO of Integrum Energy?", "ground_truth_doc_ids": [3]},
#         # add your labeled queries here
#     ]
#     eval_metrics = run_evaluation_example(rag, example_testset)
#     logger.info(f"Eval summary: {eval_metrics}")






import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
import sys
import os
import pickle
import re
from typing import List, Optional

import faiss
import numpy as np
from dotenv import load_dotenv
from openai import OpenAI
from rank_bm25 import BM25Okapi
from langchain.text_splitter import RecursiveCharacterTextSplitter
import tiktoken
from langchain_google_genai import GoogleGenerativeAIEmbeddings

load_dotenv()


os.environ["GOOGLE_API_KEY"] = os.getenv('GEMINI_API_KEY')

# -----------------------------
# Logger Setup
# -----------------------------
import logging as logger

# -----------------------------
# Helper Functions
# -----------------------------
def normalize_text(text: str) -> str:
    """Fix special characters and whitespace issues."""
    return text.replace("–", "-").replace("\xa0", " ").strip()

def count_tokens(text: str, model: str = "gpt-4o") -> int:
    """Accurate token counting using tiktoken."""
    enc = tiktoken.encoding_for_model(model)
    return len(enc.encode(text))

# -----------------------------
# Answer Generation
# -----------------------------
def generate_answer(query: str, context: str, openai_client: OpenAI) -> str:
    prompt = f"""
You are an expert AI assistant for Integrum Energy.
Answer the question using ONLY the context below.
If the answer is not in the context, politely say "I don't know".

Context:
{context}

Question: {query}
Answer:
"""
    response = openai_client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {"role": "system", "content": "You are a helpful and professional AI assistant."},
            {"role": "user", "content": prompt}
        ]
    )
    return response.choices[0].message.content

# -----------------------------
# Advanced RAG with FAISS + BM25
# -----------------------------

class RAGRetrieverFAISS:
    def __init__(self, persist_dir: str = "./faiss_index"):
        self.persist_dir = os.path.abspath(persist_dir)
        os.makedirs(self.persist_dir, exist_ok=True)
        self.index_file = os.path.join(self.persist_dir, "index.faiss")
        self.docs_file = os.path.join(self.persist_dir, "docs.pkl")
        self.meta_file = os.path.join(self.persist_dir, "meta.pkl")

        self.docs: List[str] = []
        self.metadata: List[dict] = []
        self.embeddings: Optional[np.ndarray] = None
        self.index: Optional[faiss.IndexIDMap] = None

        self.bm25 = None
        self.tokenized_docs: List[List[str]] = []

        self.openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

        self.embedding_provider = "gemini"


        
        self.gemini_client = GoogleGenerativeAIEmbeddings(
            model="models/gemini-embedding-001"
            # api_key=
        )

    # -------------------------
    # Tokenizer for BM25
    # -------------------------
    def tokenize(self, text: str) -> List[str]:
        return re.findall(r"\w+", text.lower())

    # -------------------------
    # Ingest file
    # -------------------------
    def ingest_file(self, txt_file: str, chunk_size: int = 500, chunk_overlap: int = 50):
        txt_file = os.path.abspath(txt_file)
        if not os.path.exists(txt_file):
            raise FileNotFoundError(f"File not found: {txt_file}")

        with open(txt_file, "r", encoding="utf-8") as f:
            text = normalize_text(f.read())

        splitter = RecursiveCharacterTextSplitter(chunk_size=chunk_size, chunk_overlap=chunk_overlap)
        chunks = [normalize_text(d) for d in splitter.split_text(text)]

        for i, chunk in enumerate(chunks):
            self.docs.append(chunk)
            self.metadata.append({
                "source": txt_file,
                "chunk_id": f"{os.path.basename(txt_file)}_chunk_{i}"
            })
            self.tokenized_docs.append(self.tokenize(chunk))

        # Build BM25
        self.bm25 = BM25Okapi(self.tokenized_docs)
        logger.info(f"Ingested {len(chunks)} chunks from {txt_file}")

    # -------------------------
    # Generate embedding
    # -------------------------
    def get_embedding(self, text: str) -> np.ndarray:
        response = self.openai_client.embeddings.create(
            model="text-embedding-3-small",
            input=text
        )
        return np.array(response.data[0].embedding, dtype=np.float32)


    # def get_embedding(self, text: str) -> np.ndarray:
    #     if self.embedding_provider == "openai":
    #         response = self.openai_client.embeddings.create(
    #             model="text-embedding-3-small",
    #             input=text
    #         )
    #         return np.array(response.data[0].embedding, dtype=np.float32)

    #     elif self.embedding_provider == "gemini":
    #         # Gemini embeddings call (adjust depending on the library API)
    #         emb = self.gemini_client.embed_query(text)
    #         return np.array(emb, dtype=np.float32)

    #     else:
    #         raise ValueError(f"Unsupported embedding provider: {self.embedding_provider}")

    


    # -------------------------
    # Build/load FAISS index
    # -------------------------
    def build_or_load_index(self):
        if os.path.exists(self.index_file) and os.path.exists(self.docs_file) and os.path.exists(self.meta_file):
            self.index = faiss.read_index(self.index_file)
            with open(self.docs_file, "rb") as f:
                self.docs = pickle.load(f)
            with open(self.meta_file, "rb") as f:
                self.metadata = pickle.load(f)
            self.tokenized_docs = [self.tokenize(d) for d in self.docs]
            self.bm25 = BM25Okapi(self.tokenized_docs)
            logger.info(f"Loaded existing FAISS index with {len(self.docs)} documents.")
        else:
            self.embeddings = np.array([self.get_embedding(d) for d in self.docs], dtype=np.float32)
            dim = self.embeddings.shape[1]
            self.index = faiss.IndexIDMap(faiss.IndexFlatL2(dim))
            ids = np.arange(len(self.docs))
            self.index.add_with_ids(self.embeddings, ids)
            faiss.write_index(self.index, self.index_file)
            with open(self.docs_file, "wb") as f:
                pickle.dump(self.docs, f)
            with open(self.meta_file, "wb") as f:
                pickle.dump(self.metadata, f)
            logger.info(f"Created new FAISS index with {len(self.docs)} documents.")

    # -------------------------
    # Add documents incrementally
    # -------------------------
    def add_documents_incremental(self, new_docs: List[str], source_name: str):
        new_embeddings = np.array([self.get_embedding(d) for d in new_docs], dtype=np.float32)
        start_id = len(self.docs)
        ids = np.arange(start_id, start_id + len(new_docs))
        self.index.add_with_ids(new_embeddings, ids)

        for i, doc in enumerate(new_docs):
            self.docs.append(doc)
            self.metadata.append({"source": source_name, "chunk_id": f"{source_name}_chunk_{start_id+i}"})
            self.tokenized_docs.append(self.tokenize(doc))

        self.bm25 = BM25Okapi(self.tokenized_docs)

        faiss.write_index(self.index, self.index_file)
        with open(self.docs_file, "wb") as f:
            pickle.dump(self.docs, f)
        with open(self.meta_file, "wb") as f:
            pickle.dump(self.metadata, f)
        logger.info(f"Added {len(new_docs)} new documents incrementally.")

    # -------------------------
    # Delete document
    # -------------------------
    def delete_document(self, doc_id: int):
        if doc_id < 0 or doc_id >= len(self.docs):
            logger.error(f"Invalid document ID: {doc_id}")
            return

        # Remove from docs/meta/tokenized_docs
        self.docs.pop(doc_id)
        self.metadata.pop(doc_id)
        self.tokenized_docs.pop(doc_id)
        self.bm25 = BM25Okapi(self.tokenized_docs) if self.docs else None

        # Remove from FAISS by rebuilding ID map
        if self.docs:
            self.embeddings = np.array([self.get_embedding(d) for d in self.docs], dtype=np.float32)
            dim = self.embeddings.shape[1]
            self.index = faiss.IndexIDMap(faiss.IndexFlatL2(dim))
            ids = np.arange(len(self.docs))
            self.index.add_with_ids(self.embeddings, ids)
        else:
            self.index = None

        faiss.write_index(self.index, self.index_file)
        with open(self.docs_file, "wb") as f:
            pickle.dump(self.docs, f)
        with open(self.meta_file, "wb") as f:
            pickle.dump(self.metadata, f)

        logger.info(f"Document ID {doc_id} deleted and index updated.")

    # -------------------------
    # Retrieve top-k chunks
    # -------------------------
    def retrieve(self, query: str, k: int = 5, mode: str = "hybrid") -> List[str]:
        results = []

        faiss_docs = []
        if self.index:
            query_emb = self.get_embedding(query).reshape(1, -1)
            distances, indices = self.index.search(query_emb, k)
            faiss_docs = [(self.docs[i], float(distances[0][j])) for j, i in enumerate(indices[0]) if i < len(self.docs)]

        bm25_docs = []
        if self.bm25:
            tokenized_query = self.tokenize(query)
            scores = self.bm25.get_scores(tokenized_query)
            top_indices = np.argsort(scores)[::-1][:k]
            bm25_docs = [(self.docs[i], float(scores[i])) for i in top_indices]

        if mode == "faiss":
            results = [doc for doc, _ in faiss_docs]
        elif mode == "bm25":
            results = [doc for doc, _ in bm25_docs]
        elif mode == "hybrid":
            faiss_scores = np.array([1/(1+d) for _, d in faiss_docs])
            bm25_scores  = np.array([s for _, s in bm25_docs])
            if len(faiss_scores) > 0:
                faiss_scores = (faiss_scores - faiss_scores.min()) / (np.ptp(faiss_scores) + 1e-8)
            if len(bm25_scores) > 0:
                bm25_scores = (bm25_scores - bm25_scores.min()) / (np.ptp(bm25_scores) + 1e-8)
            combined = {}
            for (doc, _), f in zip(faiss_docs, faiss_scores):
                combined[doc] = combined.get(doc, 0) + 0.5*f
            for (doc, _), b in zip(bm25_docs, bm25_scores):
                combined[doc] = combined.get(doc, 0) + 0.5*b
            results = [doc for doc, _ in sorted(combined.items(), key=lambda x: x[1], reverse=True)][:k]

        if not results:
            logger.warning("No relevant documents found for the query.")
        return results

    # -------------------------
    # Assemble context
    # -------------------------
    def assemble_context(self, docs: List[str], max_tokens: int = 2500) -> str:
        context = ""
        tokens = 0
        for doc in docs:
            t_count = count_tokens(doc)
            if tokens + t_count > max_tokens:
                break
            context += doc + "\n"
            tokens += t_count
        return context.strip()


    # -------------------------
    # Document utilities
    # -------------------------
    def list_documents(self, limit: int = 20):
        logger.info(f"Total documents: {len(self.docs)}")
        for i, meta in enumerate(self.metadata[:limit]):
            logger.info(f"ID: {i}, Source: {meta['source']}, Chunk ID: {meta['chunk_id']}")

    def view_document(self, doc_id: int):
        if doc_id < 0 or doc_id >= len(self.docs):
            logger.error(f"Invalid document ID: {doc_id}")
            return
        logger.info(f"Document ID: {doc_id}")
        logger.info(f"Source: {self.metadata[doc_id]['source']}")
        logger.info(f"Chunk ID: {self.metadata[doc_id]['chunk_id']}")
        logger.info("Content:")
        logger.info(self.docs[doc_id])

    def delete_document(self, doc_id: int):
        if doc_id < 0 or doc_id >= len(self.docs):
            logger.error(f"Invalid document ID: {doc_id}")
            return

        self.docs.pop(doc_id)
        self.metadata.pop(doc_id)

        if len(self.docs) > 0:
            self.embeddings = np.array([self.get_embedding(d) for d in self.docs], dtype=np.float32)
            dim = self.embeddings.shape[1]
            self.index = faiss.IndexFlatL2(dim)
            self.index.add(self.embeddings)
        else:
            self.index = None

        self.tokenized_docs = [self.tokenize(doc) for doc in self.docs]
        self.bm25 = BM25Okapi(self.tokenized_docs) if self.docs else None

        faiss.write_index(self.index, self.index_file)
        with open(self.docs_file, "wb") as f:
            pickle.dump(self.docs, f)
        with open(self.meta_file, "wb") as f:
            pickle.dump(self.metadata, f)

        logger.info(f"Document ID {doc_id} deleted and index updated.")


# -----------------------------
# Example Usage
# -----------------------------
if __name__ == "__main__":
    rag = RAGRetrieverFAISS()

    # Ingest initial txt file
    txt_path = os.path.join("mcp_bot", "integrum_energy.txt")
    rag.ingest_file(txt_path)

    # Build or load FAISS index
    rag.build_or_load_index()

    # Example query
    query = "Who is the COO of Integrum Energy?"
    top_docs = rag.retrieve(query, k=5, mode="hybrid")
    context = rag.assemble_context(top_docs)

    if top_docs:
        answer = generate_answer(query, context, rag.openai_client)
        logger.info(f"\n[ANSWER]: {answer}")
    else:
        logger.info("\n[ANSWER]: I don't know")
