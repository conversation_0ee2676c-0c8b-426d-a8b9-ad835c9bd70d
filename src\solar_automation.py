import os
import json
from pathlib import Path
from typing import Dict, Any, Optional, List

import pandas as pd

from helper.utils import (
    generate_solar_dgr_pdf,
    get_dynamic_dates,
    fetch_data_total,
    get_capacity_from_csv,
    get_inverter_metadata_from_csv,
    _safe_mean,
    _safe_sum,
)
from helper.logger_setup import setup_logger
from helper.storage_s3 import upload_file_s3
from DB.db_ops import insert_solar_data_db, insert_solar_inverter_data_db

# -------------------------------
# Constants
# -------------------------------
STATIC_REPORT_DIR = Path("static") / "solar_final_report"
S3_REPORT_PREFIX = "solar_reports/"

LOGGER = setup_logger("solar_automation", "solar_automation.log")


# -------------------------------
# Data Fetching
# -------------------------------
def fetch_all_solar_data(
    plant_name: str,
    start_date: str,
    current_month_start: str,
    last_30_days_start: str,
    current_year_start: str,
    yearly: str,
    condition_poa: Dict[str, str],
    condition_pr: Dict[str, str],
    condition_generation: Dict[str, str],
    condition_monthly_pr: Dict[str, str]
) -> Dict[str, pd.DataFrame]:
    """
    Fetch all solar data sequentially to reduce RAM usage.
    """
    LOGGER.info(f"[Data Fetch] Plant: {plant_name}")

    return {
        "poa_data": fetch_data_total(plant_name, ["Daily POA Energy"], "Plant", start_date, start_date, condition_poa),
        "pr_data": fetch_data_total(plant_name, ["PR"], "Plant", start_date, start_date, condition_pr),
        "daily_generation": fetch_data_total(plant_name, ["Daily Energy"], "Plant", start_date, start_date, condition_generation),
        "generation_monthly_value": fetch_data_total(plant_name, ["Daily Energy"], "Plant", current_month_start, start_date, condition_generation),
        "pr_data_monthly_value": fetch_data_total(plant_name, ["PR"], "Plant", current_month_start, start_date, condition_monthly_pr),
        "poa_data_monthly_value": fetch_data_total(plant_name, ["Daily POA Energy"], "Plant", current_month_start, start_date, condition_poa),



        "pr_data_inverter": fetch_data_total(plant_name, ["PR"], "Inverter", start_date, start_date, condition_pr),
        "daily_generation_inverter": fetch_data_total(plant_name, ["Daily Energy"], "Inverter", start_date, start_date, condition_generation),
        "pr_data_inverter_monthly": fetch_data_total(plant_name, ["PR"], "Inverter", current_month_start, start_date, condition_monthly_pr),
        "daily_generation_inverter_monthly": fetch_data_total(plant_name, ["Daily Energy"], "Inverter", current_month_start, start_date, condition_generation),
    }


# -------------------------------
# CSV & Metadata Handling
# -------------------------------
def merge_csv_with_inverter_metadata(csv_path: Path, plant_name: str) -> Optional[str]:
    """
    Merge CSV report data with inverter metadata and return as JSON string.
    """
    if not csv_path.exists():
        LOGGER.warning(f"No CSV report found at {csv_path}")
        return None

    try:
        csv_report_list = pd.read_csv(csv_path).to_dict(orient="records")

        inverter_metadata_str = get_inverter_metadata_from_csv(plant_name)
        inverter_metadata_list = _safe_parse_metadata(inverter_metadata_str)

        # Index data by 'INV' for quick merging
        meta_by_inv = {str(d.get("INV")).strip(): d for d in inverter_metadata_list}
        csv_by_inv = {str(d.get("INV")).strip(): d for d in csv_report_list}

        # Collect all keys from metadata
        all_keys = {k for d in inverter_metadata_list for k in d}

        merged_list = []
        for inv_name, meta in meta_by_inv.items():
            report_row = csv_by_inv.get(inv_name, {}).copy()
            for k in all_keys:
                if k not in report_row and k in meta:
                    report_row[k] = meta[k]
            report_row["INV"] = inv_name
            merged_list.append(report_row)

        # Include extra CSV rows not in metadata
        for inv_name, report_row in csv_by_inv.items():
            if inv_name not in meta_by_inv:
                merged_list.append(report_row)

        # Filter out entries where INV is empty or invalid
        filtered_list = [row for row in merged_list if str(row.get("INV", "")).strip() not in ("", "0", "nan")]

        return json.dumps(filtered_list)

    except Exception as e:
        LOGGER.error(f"Error merging CSV with inverter metadata: {e}", exc_info=True)
        return None


def _safe_parse_metadata(raw_str: Optional[str]) -> List[Dict[str, Any]]:
    """
    Safely parse inverter metadata JSON from CSV helper.
    """
    if not raw_str or raw_str == "N/A":
        return []
    try:
        return json.loads(raw_str.replace('""', '"'))
    except Exception as e:
        LOGGER.error(f"Error parsing inverter metadata: {e}")
        return []


# -------------------------------
# Main Report Generation
# -------------------------------
def generate_solar_automation_report(
    plant_name: str,
    start_date: str,
    customer_name: str,
    project: str
) -> Optional[Path]:
    """
    Generate and upload a daily solar automation report for a given plant.
    """
    LOGGER.info(f"[Start Report] Plant: {plant_name}, Date: {start_date}, Customer: {customer_name}")

    try:
        # Define fetch conditions
        condition_poa = {"Daily POA Energy": "last"}
        condition_daily_pr = {"PR": "last"}
        condition_monthly_pr = {"PR": "mean"}
        condition_generation = {"Daily Energy": "max"}

        # Get date ranges
        current_month_start, _, _, _, _, last_30_days_start, current_year_start, last_year_date = get_dynamic_dates(start_date)

        # Fetch all required data
        data = fetch_all_solar_data(
            plant_name, start_date, current_month_start, last_30_days_start,
            current_year_start, last_year_date, condition_poa, condition_daily_pr,
            condition_generation, condition_monthly_pr
        )

        # Extract and compute key metrics
        total_generation = _safe_sum(data["daily_generation"])
        month_gen_value = _safe_sum(data["generation_monthly_value"])
        month_pr_value = _safe_mean(data["pr_data_monthly_value"])
        daily_pr_percentage = _safe_mean(data["pr_data"])
        monthly_poa_value = _safe_mean(data["poa_data_monthly_value"])
        avg_poa = _safe_mean(data["poa_data"])

        capacity = get_capacity_from_csv(plant_name)

        # Prepare report path
        STATIC_REPORT_DIR.mkdir(parents=True, exist_ok=True)
        final_pdf_path = STATIC_REPORT_DIR / f"{plant_name}_DGR_{start_date}.jpg"

        # Generate PDF report
        summary_pdf = generate_solar_dgr_pdf(
            start_date, customer_name, avg_poa, daily_pr_percentage,
            total_generation, month_gen_value, month_pr_value,
            monthly_poa_value, capacity, final_pdf_path, comment_text=None
        )

        LOGGER.info(f"[Report Generated] {summary_pdf}")

        # Upload to S3
        s3_path = f"{S3_REPORT_PREFIX}{plant_name}_DGR_{start_date}.jpg"
        upload_file_s3(summary_pdf, s3_path)

        # Generate CSV report for inverter-level data (similar to wind automation)
        csv_report = None
        try:
            # Create a temporary CSV with inverter data for metadata merging
            inverter_metadata_str = get_inverter_metadata_from_csv(plant_name)
            inverter_metadata_list = _safe_parse_metadata(inverter_metadata_str)

            if inverter_metadata_list:
                # Create CSV data from inverter metadata structure
                csv_data = []
                for meta in inverter_metadata_list:
                    inv_name = meta.get("INV", "")
                    csv_data.append({
                        "INV": inv_name,
                        "Daily Energy": meta.get("Daily Energy", 0),
                        "PR": meta.get("PR", 0),
                        "Daily POA Energy": meta.get("Daily POA Energy", 0)
                    })

                # Save temporary CSV
                temp_csv_path = STATIC_REPORT_DIR / f"{plant_name}_temp_inverter_{start_date}.csv"
                pd.DataFrame(csv_data).to_csv(temp_csv_path, index=False)
                csv_report = str(temp_csv_path)
        except Exception as e:
            LOGGER.error(f"Error creating CSV report for {plant_name}: {e}", exc_info=True)

        # Insert into DB
        record = {
            "date": start_date,
            "plant_short_name": plant_name,
            "plant_long_name": customer_name,
            "generation": round(total_generation, 2),
            "pr": round(daily_pr_percentage, 2),
            "poa": round(avg_poa, 2),
            "generation_monthly": round(month_gen_value, 2),
            "pr_monthly": round(month_pr_value, 2),
            "poa_monthly": round(monthly_poa_value, 2),
            "approved": 0,
            "review": 0,
            "action_performed": 0,
            "dgr_path": str(summary_pdf),
            "csv_report_data": merge_csv_with_inverter_metadata(Path(csv_report), plant_name) if csv_report else None
        }

        insert_solar_data_db([record])
        LOGGER.info(f"[DB Insert] Solar data saved for {plant_name}")

        # --- Inverter-wise data integration with metadata ---
        from datetime import datetime

        try:
            # Read inverter metadata
            inverter_metadata_str = get_inverter_metadata_from_csv(plant_name)
            inverter_metadata_list = _safe_parse_metadata(inverter_metadata_str)

            # Extract inverter-wise dataframes
            pr_df = data.get("pr_data_inverter")
            gen_df = data.get("daily_generation_inverter")
            pr_monthly_df = data.get("pr_data_inverter_monthly")
            gen_monthly_df = data.get("daily_generation_inverter_monthly")

            # Read CSV as dict indexed by INV name if CSV was created
            csv_by_inv = {}
            if csv_report and Path(csv_report).exists():
                df_inverter = pd.read_csv(csv_report)
                csv_by_inv = {str(row.get("INV")).strip(): row for _, row in df_inverter.iterrows()}

            inverter_records = []

            # Process each inverter from metadata
            for meta in inverter_metadata_list:
                inv_name = str(meta.get("INV", "")).strip()
                if not inv_name:
                    continue

                csv_row = csv_by_inv.get(inv_name, {})

                # Use daily values from CSV if present, else calculate from dataframes
                pr = float(csv_row.get("PR", 0)) if csv_row.get("PR") else (
                    float(pr_df[f"{inv_name}.PR"].mean()) if pr_df is not None and f"{inv_name}.PR" in pr_df.columns else 0
                )
                generation = float(csv_row.get("Daily Energy", 0)) if csv_row.get("Daily Energy") else (
                    float(gen_df[f"{inv_name}.Daily Energy"].sum()) if gen_df is not None and f"{inv_name}.Daily Energy" in gen_df.columns else 0
                )

                # Calculate monthly values for this inverter
                pr_monthly = None
                generation_monthly = None
                if pr_monthly_df is not None and f"{inv_name}.PR" in pr_monthly_df.columns:
                    pr_monthly = float(pr_monthly_df[f"{inv_name}.PR"].mean())
                if gen_monthly_df is not None and f"{inv_name}.Daily Energy" in gen_monthly_df.columns:
                    generation_monthly = float(gen_monthly_df[f"{inv_name}.Daily Energy"].sum())

                inverter_record = {
                    "date": start_date,
                    "plant_id": plant_name,
                    "plant_name": customer_name,
                    "inverter_name": inv_name,
                    "generation": generation,
                    "pr": pr,
                    "poa": avg_poa,
                    "generation_monthly": generation_monthly,
                    "pr_monthly": pr_monthly,
                    "poa_monthly": monthly_poa_value,
                    "created_at": datetime.now(),
                    "updated_at": datetime.now()
                }
                inverter_records.append(inverter_record)

            if inverter_records:
                insert_solar_inverter_data_db(inverter_records)
                LOGGER.info(f"[Success] Inverter-wise data inserted for {plant_name} ({len(inverter_records)} inverters)")
        except Exception as e:
            LOGGER.error(f"[Error] Inverter-wise data insertion failed: {e}", exc_info=True)
            # Fallback: insert zeroed-out inverter records using metadata
            try:
                inverter_metadata_str = get_inverter_metadata_from_csv(plant_name)
                inverter_metadata_list = _safe_parse_metadata(inverter_metadata_str)
                from datetime import datetime
                zero_inverter_records = []
                for meta in inverter_metadata_list:
                    inv_name = str(meta.get("INV", "")).strip()
                    if inv_name:
                        zero_inverter_records.append({
                            "date": start_date,
                            "plant_id": plant_name,
                            "plant_name": customer_name,
                            "inverter_name": inv_name,
                            "generation": 0,
                            "pr": 0,
                            "poa": 0,
                            "generation_monthly": 0,
                            "pr_monthly": 0,
                            "poa_monthly": 0,
                            "created_at": datetime.now(),
                            "updated_at": datetime.now()
                        })
                if zero_inverter_records:
                    insert_solar_inverter_data_db(zero_inverter_records)
                    LOGGER.info(f"[Fallback] Zero inverter data inserted for {plant_name} ({len(zero_inverter_records)} inverters)")
            except Exception as e2:
                LOGGER.error(f"[DB Error] Failed inserting zero inverter data for {plant_name}: {e2}", exc_info=True)

        LOGGER.info(f"[Success] Report generated and stored for {plant_name}")

        # Cleanup temporary CSV file
        if csv_report and Path(csv_report).exists():
            Path(csv_report).unlink()

        return summary_pdf

    except Exception as e:
        LOGGER.error(f"[Error] Failed generating report for {plant_name}: {e}", exc_info=True)
        _insert_zero_solar_data(plant_name, start_date, customer_name)
        return None




def _insert_zero_solar_data(plant_name: str, date: str, customer_name: str) -> None:
    """
    Insert a zeroed record into DB when solar report generation fails.
    """
    final_pdf_path = STATIC_REPORT_DIR / f"{plant_name}_DGR_{date}.jpg"

    # Fallback: parse and normalize inverter metadata for DB insertion
    inverter_metadata_str = get_inverter_metadata_from_csv(plant_name)
    inverter_metadata_list = _safe_parse_metadata(inverter_metadata_str)
    csv_report_data = json.dumps(inverter_metadata_list)

    zero_record = {
        "date": date,
        "plant_short_name": plant_name,
        "plant_long_name": customer_name,
        "generation": 0,
        "pr": 0,
        "poa": 0,
        "generation_monthly": 0,
        "pr_monthly": 0,
        "poa_monthly": 0,
        "approved": 0,
        "review": 0,
        "action_performed": 0,
        "dgr_path": str(final_pdf_path),
        "csv_report_data": csv_report_data
    }
    try:
        insert_solar_data_db([zero_record])
        LOGGER.info(f"[Fallback] Zero solar data inserted for {plant_name}")
    except Exception as e:
        LOGGER.error(f"[DB Error] Failed inserting zero solar data: {e}", exc_info=True)
