import json
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple

import pandas as pd

from helper.utils import (
    get_dynamic_dates,
    generate_combined_both_pdf,
    fetch_data_total,
    get_capacity_from_csv,
    get_turbine_metadata_from_csv,
    generate_dgr_wind_report,
    _safe_mean,
    _safe_sum,
)
from helper.logger_setup import setup_logger
from helper.storage_s3 import upload_file_s3
from DB.db_ops import insert_both_data_db, insert_solar_inverter_data_db, insert_wind_turbine_data_db

# -------------------------------
# Constants
# -------------------------------
STATIC_REPORT_DIR = Path("static") / "both_final_report"
S3_REPORT_PREFIX = "both_final_report/"

LOGGER = setup_logger("both_plants_automation", "both_plants_automation.log")


# -------------------------------
# Data Fetching
# -------------------------------
def fetch_all_solar_data(
    plant_name: str,
    start_date: str,
    current_month_start: str,
    last_30_days_start: str,
    current_year_start: str,
    yearly: str,
    condition_poa: Dict[str, str],
    condition_pr: Dict[str, str],
    condition_generation: Dict[str, str],
    condition_monthly_pr: Dict[str, str],
) -> Dict[str, pd.DataFrame]:
    """
    Fetch all solar data sequentially to reduce RAM usage.
    """
    LOGGER.info(f"[Data Fetch] Solar | Plant: {plant_name}")

    return {
        "poa_data": fetch_data_total(
            plant_name, ["Daily POA Energy"], "Plant", start_date, start_date, condition_poa
        ),
        "pr_data": fetch_data_total(
            plant_name, ["PR"], "Plant", start_date, start_date, condition_pr
        ),
        "daily_generation": fetch_data_total(
            plant_name, ["Daily Energy"], "Plant", start_date, start_date, condition_generation
        ),
        "generation_monthly_value": fetch_data_total(
            plant_name, ["Daily Energy"], "Plant", current_month_start, start_date, condition_generation
        ),
        "pr_data_monthly_value": fetch_data_total(
            plant_name, ["PR"], "Plant", current_month_start, start_date, condition_monthly_pr
        ),
        "poa_data_monthly_value": fetch_data_total(
            plant_name, ["Daily POA Energy"], "Plant", current_month_start, start_date, condition_poa
        ),
    }


def fetch_all_wind_data(
    plant_name: str,
    start_date: str,
    current_month_start: str,
    last_30_days_start: str,
    current_year_start: str,
    yearly_date: str,
    condition_wind: Dict[str, str],
    condition_generation: Dict[str, str],
) -> Dict[str, pd.DataFrame]:
    """
    Fetch all wind data sequentially to reduce RAM usage.
    """
    LOGGER.info(f"[Data Fetch] Wind  | Plant: {plant_name}")

    return {
        "wind_speed_data_value": fetch_data_total(
            plant_name, ["WTUR.Wind-Speed"], "Turbine", start_date, start_date, condition_wind
        ),
        "daily_generation_value": fetch_data_total(
            plant_name, ["WTUR.Generation today"], "Turbine", start_date, start_date, condition_generation
        ),
        "wind_data_monthly_value": fetch_data_total(
            plant_name, ["WTUR.Wind-Speed"], "Turbine", current_month_start, start_date, condition_wind
        ),
        "generation_monthly_value": fetch_data_total(
            plant_name, ["WTUR.Generation today"], "Turbine", current_month_start, start_date, condition_generation
        ),
    }


# -------------------------------
# CSV & Metadata Handling
# -------------------------------

def _safe_parse_metadata(raw_str: Optional[str]) -> List[Dict[str, Any]]:
    """
    Safely parse turbine metadata JSON from CSV helper.
    """
    if not raw_str or raw_str == "N/A":
        return []
    try:
        return json.loads(raw_str.replace('""', '"'))
    except Exception as e:
        LOGGER.error(f"Error parsing turbine metadata: {e}")
        return []


def merge_csv_with_metadata(csv_path: Path, plant_name: str) -> Optional[str]:
    """
    Merge CSV report data with turbine metadata and return as JSON string.
    """
    if not csv_path.exists():
        LOGGER.warning(f"No CSV report found at {csv_path}")
        return None

    try:
        csv_report_list = pd.read_csv(csv_path).to_dict(orient="records")

        turbine_metadata_str = get_turbine_metadata_from_csv(plant_name)
        turbine_metadata_list = _safe_parse_metadata(turbine_metadata_str)

        # Index data by 'Loc No' for quick merging
        meta_by_loc = {str(d.get("Loc No")).strip(): d for d in turbine_metadata_list}
        csv_by_loc = {str(d.get("Loc No")).strip(): d for d in csv_report_list}

        # Collect all keys from metadata
        all_keys = {k for d in turbine_metadata_list for k in d}

        merged_list = []
        for loc_no, meta in meta_by_loc.items():
            report_row = csv_by_loc.get(loc_no, {}).copy()
            for k in all_keys:
                if k not in report_row and k in meta:
                    report_row[k] = meta[k]
            report_row["Loc No"] = loc_no
            merged_list.append(report_row)

        # Include extra CSV rows not in metadata
        for loc_no, report_row in csv_by_loc.items():
            if loc_no not in meta_by_loc:
                merged_list.append(report_row)

        # Filter out entries where Loc No is 0 (as string or int)
        filtered_list = [row for row in merged_list if str(row.get("Loc No", "")).strip() not in ("0", 0)]

        return json.dumps(filtered_list)

    except Exception as e:
        LOGGER.error(f"Error merging CSV with metadata: {e}", exc_info=True)
        return None


# -------------------------------
# KPI Computation
# -------------------------------

def compute_solar_kpis(
    plant_name: str,
    start_date: str,
) -> Tuple[float, float, float, float, float, float]:
    """
    Compute and return key solar KPIs for the given date.
    Returns (avg_poa, daily_pr, total_generation, month_gen_value, month_pr_value, monthly_poa_value)
    """
    LOGGER.info(f"[Start KPIs] Solar | Plant: {plant_name}, Date: {start_date}")

    return _compute_solar_kpis_single(plant_name, start_date)

def _compute_solar_kpis_single(
    plant_name: str,
    start_date: str,
) -> Tuple[float, float, float, float, float, float]:
    """
    Compute KPIs for a single solar plant (helper for hardcoded mapping logic).
    """
    # Define fetch conditions
    condition_poa = {"Daily POA Energy": "last"}
    condition_daily_pr = {"PR": "last"}
    condition_monthly_pr = {"PR": "mean"}
    condition_generation = {"Daily Energy": "max"}

    # Get date ranges
    current_month_start, _, _, _, _, last_30_days_start, current_year_start, last_year_date = get_dynamic_dates(start_date)

    # Fetch all required data
    data = fetch_all_solar_data(
        plant_name,
        start_date,
        current_month_start,
        last_30_days_start,
        current_year_start,
        last_year_date,
        condition_poa,
        condition_daily_pr,
        condition_generation,
        condition_monthly_pr,
    )

    # Extract and compute key metrics
    total_generation = _safe_sum(data["daily_generation"])  # kWh/MWh as per source
    month_gen_value = _safe_sum(data["generation_monthly_value"])  # monthly gen sum
    month_pr_value = _safe_mean(data["pr_data_monthly_value"])  # monthly PR mean
    daily_pr_percentage = _safe_mean(data["pr_data"])  # today's PR
    monthly_poa_value = _safe_mean(data["poa_data_monthly_value"])  # monthly POA mean
    avg_poa = _safe_mean(data["poa_data"])  # today's POA

    return avg_poa, daily_pr_percentage, total_generation, month_gen_value, month_pr_value, monthly_poa_value


def compute_wind_kpis(
    plant_name: str,
    start_date: str,
    ma_percent: float,
) -> Tuple[float, float, float, float, pd.DataFrame, pd.DataFrame]:
    """
    Compute and return key wind KPIs and raw frames needed for CSV.
    Returns (avg_wind_speed, total_generation, monthly_wind, monthly_generation, wind_speed_df, daily_generation_df)
    """
    LOGGER.info(f"[Start KPIs] Wind  | Plant: {plant_name}, Date: {start_date}")

    current_month_start, _, _, _, _, last_30_days_start, current_year_start, last_year_date = get_dynamic_dates(start_date)
    condition_wind = {"Wind-Speed": "mean"}
    condition_generation = {"Generation today": "last"}

    data = fetch_all_wind_data(
        plant_name,
        start_date,
        current_month_start,
        last_30_days_start,
        current_year_start,
        last_year_date,
        condition_wind,
        condition_generation,
    )

    avg_wind_speed = _safe_mean(data["wind_speed_data_value"])  # today's wind speed
    total_generation = _safe_sum(data["daily_generation_value"])  # today's generation
    monthly_wind = _safe_mean(data["wind_data_monthly_value"])  # monthly wind mean
    monthly_generation = _safe_sum(data["generation_monthly_value"])  # monthly generation sum

    return (
        avg_wind_speed,
        total_generation,
        monthly_wind,
        monthly_generation,
        data["wind_speed_data_value"],
        data["daily_generation_value"],
    )


# -------------------------------
# Main Combined Report Generation
# -------------------------------

def combined_both_plants(
    plant_name_solar: str,
    plant_name_wind: str,
    start_date: str,
    customer_name: str,
    project: str,
    ma_percent: float,
) -> Optional[Path]:
    """
    Enhanced: Ensures correct types for DB, logs all records, and warns on empty data.
    """
    from datetime import datetime, date
    # Convert start_date to date object if string
    if isinstance(start_date, str):
        try:
            start_date_obj = datetime.strptime(start_date, "%Y-%m-%d").date()
        except Exception:
            try:
                start_date_obj = datetime.strptime(start_date, "%Y/%m/%d").date()
            except Exception:
                LOGGER.error(f"Could not parse start_date: {start_date}")
                start_date_obj = start_date
    else:
        start_date_obj = start_date
    """
    Generates a combined report for both solar and wind plants and stores it to S3 and DB.
    Returns the path to the generated combined report, or None on failure.
    """
    # No paired plants logic; use plant_name_solar as is

    LOGGER.info(
        f"[Start Report] Both | Solar: {plant_name_solar}, Wind: {plant_name_wind}, Date: {start_date}"
    )

    try:
        # Compute KPIs for both plants
        (
            daily_poa,
            daily_pr,
            daily_gen_solar,
            month_gen_solar,
            month_pr_value,
            monthly_poa_solar,
        ) = compute_solar_kpis(plant_name_solar, start_date)

        (
            daily_wind_speed,
            daily_gen_wind,
            monthly_wind,
            monthly_gen_wind,
            wind_speed_df,
            daily_generation_df,
        ) = compute_wind_kpis(plant_name_wind, start_date, ma_percent)

        # Capacities
        capacity_solar = get_capacity_from_csv(plant_name_solar)
        capacity_wind = get_capacity_from_csv(plant_name_wind)

        # Paths
        STATIC_REPORT_DIR.mkdir(parents=True, exist_ok=True)
        final_pdf_path = STATIC_REPORT_DIR / f"{plant_name_solar}_DGR_{start_date}.jpg"

        # Wind CSV report for table merge on combined sheet
        csv_report_path_str = generate_dgr_wind_report(
            plant_name_wind,
            wind_speed_df,
            daily_generation_df,
            start_date,
            customer_name,
            project,
            ma_percent,
        )
        csv_report_path = Path(csv_report_path_str)

        # Generate combined PDF
        summary_pdf = generate_combined_both_pdf(
            start_date,
            customer_name,
            project,
            daily_poa,
            daily_pr,
            daily_gen_solar,
            month_gen_solar,
            month_pr_value,
            monthly_poa_solar,
            daily_wind_speed,
            daily_gen_wind,
            monthly_wind,
            monthly_gen_wind,
            capacity_wind,
            capacity_solar,
            csv_report_path_str,
            plant_name_solar,
            plant_name_wind,
            final_pdf_path,
        )

        # Upload to S3
        s3_relative_path = f"{S3_REPORT_PREFIX}{plant_name_solar}_DGR_{start_date}.jpg"
        upload_file_s3(summary_pdf, s3_relative_path)

        # Prepare DB record
        record = {
            "date": start_date,
            "plant_short_name_solar": plant_name_solar,
            "plant_long_name_solar": customer_name,
            "generation_solar": round(float(daily_gen_solar), 2),
            "pr": round(float(daily_pr), 2),
            "poa": round(float(daily_poa), 2),
            "generation_solar_monthly": round(float(month_gen_solar), 2),
            "pr_monthly": round(float(month_pr_value), 2),
            "poa_monthly": round(float(monthly_poa_solar), 2),
            "plant_short_name_wind": plant_name_wind,
            "plant_long_name_wind": customer_name,
            "generation_wind": round(float(daily_gen_wind), 2),
            "wind_speed": round(float(daily_wind_speed), 2),
            "generation_wind_monthly": round(float(monthly_gen_wind), 2),
            "wind_speed_monthly": round(float(monthly_wind), 2),
            "approved": 0,
            "review": 0,
            "action_performed": 0,
            "dgr_path": str(summary_pdf),
        }

        # Merge CSV with metadata and add as JSON string to data (like wind_automation.py)
        record["csv_report_data"] = merge_csv_with_metadata(csv_report_path, plant_name_wind)
        record["edit_csv_report_data"] = None

        insert_both_data_db([record])
        LOGGER.info(
            f"[Success] Both report generated + stored | Solar: {plant_name_solar}, Wind: {plant_name_wind}"
        )

        # --- Turbine-wise wind data insertion (like wind_automation.py) ---
        try:
            import pandas as pd
            # Read turbine metadata
            turbine_metadata_str = get_turbine_metadata_from_csv(plant_name_wind)
            turbine_metadata_list = _safe_parse_metadata(turbine_metadata_str)
            # Read CSV as dict indexed by Loc No
            df_turbine = pd.read_csv(csv_report_path)
            csv_by_loc = {str(row.get("Loc No")).strip(): row for _, row in df_turbine.iterrows()}
            turbine_records = []
            # Fetch monthly wind data for turbines
            current_month_start, _, _, _, _, last_30_days_start, current_year_start, last_year_date = get_dynamic_dates(start_date)
            condition_wind = {"Wind-Speed": "mean"}
            condition_generation = {"Generation today": "last"}
            wind_monthly_data = fetch_all_wind_data(
                plant_name_wind,
                start_date,
                current_month_start,
                last_30_days_start,
                current_year_start,
                last_year_date,
                condition_wind,
                condition_generation,
            )
            gen_monthly_df = wind_monthly_data.get("generation_monthly_value")
            wind_monthly_df = wind_monthly_data.get("wind_data_monthly_value")
            for meta in turbine_metadata_list:
                loc_no = str(meta.get("Loc No", "0")).strip()
                csv_row = csv_by_loc.get(loc_no, {})
                # Use daily values from CSV if present, else 0
                generation = float(csv_row.get("Daily Generation (KWh)", 0))
                avg_wind_speed = float(csv_row.get("Avg Wind Speed", 0))
                # Calculate monthly values for this turbine
                gen_monthly = None
                wind_monthly = None
                if gen_monthly_df is not None and f"{loc_no}.Generation today" in gen_monthly_df.columns:
                    gen_monthly = float(gen_monthly_df[f"{loc_no}.Generation today"].sum())
                if wind_monthly_df is not None and f"{loc_no}.Wind-Speed" in wind_monthly_df.columns:
                    wind_monthly = float(wind_monthly_df[f"{loc_no}.Wind-Speed"].mean())
                turbine_record = {
                    "date": start_date_obj,
                    "plant_id": plant_name_wind,
                    "plant_name": customer_name,
                    "turbine_name": loc_no,
                    "generation": generation,
                    "avg_wind_speed": avg_wind_speed,
                    "generation_monthly": gen_monthly,
                    "avg_wind_speed_monthly": wind_monthly,
                    "created_at": datetime.now(),
                    "updated_at": datetime.now()
                }
                # Optionally, add more fields from metadata if needed
                turbine_records.append(turbine_record)
            if not turbine_records:
                LOGGER.warning(f"[Wind] No turbine records built for {plant_name_wind} on {start_date}")
            else:
                LOGGER.info(f"[Wind] Turbine records to insert: {turbine_records}")
                result_ids = insert_wind_turbine_data_db(turbine_records)
                if not result_ids:
                    LOGGER.error(f"[Wind] Insert returned no IDs for {plant_name_wind} on {start_date}")
                else:
                    LOGGER.info(f"[Success] Turbine-wise data inserted for {plant_name_wind} ({len(result_ids)} turbines)")
        except Exception as e:
            LOGGER.error(f"[Error] Turbine-wise data insertion failed: {e}", exc_info=True)
            # Fallback: insert zeroed-out turbine records
            try:
                turbine_metadata_str = get_turbine_metadata_from_csv(plant_name_wind)
                turbine_metadata_list = _safe_parse_metadata(turbine_metadata_str)
                zero_turbine_records = []
                for meta in turbine_metadata_list:
                    loc_no = str(meta.get("Loc No", "0"))
                    zero_turbine_records.append({
                        "date": start_date_obj,
                        "plant_id": plant_name_wind,
                        "plant_name": customer_name,
                        "turbine_name": loc_no,
                        "generation": 0,
                        "avg_wind_speed": 0,
                        "generation_monthly": 0,
                        "avg_wind_speed_monthly": 0,
                        "created_at": datetime.now(),
                        "updated_at": datetime.now()
                    })
                if zero_turbine_records:
                    LOGGER.info(f"[Wind] Fallback zero turbine records: {zero_turbine_records}")
                    insert_wind_turbine_data_db(zero_turbine_records)
                    LOGGER.info(f"[Fallback] Zero turbine data inserted for {plant_name_wind} ({len(zero_turbine_records)} turbines)")
            except Exception as e2:
                LOGGER.error(f"[DB Error] Failed inserting zero turbine data for {plant_name_wind}: {e2}", exc_info=True)

        # --- Inverter-wise solar data insertion (like solar_automation.py) ---
        try:
            inverter_records = []
            # Helper to fetch inverter-wise data for a plant
            def fetch_inverter_data(plant_name, customer_name):
                # Fetch inverter-wise dataframes
                condition_poa = {"Daily POA Energy": "last"}
                condition_daily_pr = {"PR": "last"}
                condition_monthly_pr = {"PR": "mean"}
                condition_generation = {"Daily Energy": "max"}
                current_month_start, _, _, _, _, last_30_days_start, current_year_start, last_year_date = get_dynamic_dates(start_date)
                pr_df = fetch_data_total(plant_name, ["PR"], "Inverter", start_date, start_date, condition_daily_pr)
                gen_df = fetch_data_total(plant_name, ["Daily Energy"], "Inverter", start_date, start_date, condition_generation)
                pr_monthly_df = fetch_data_total(plant_name, ["PR"], "Inverter", current_month_start, start_date, condition_monthly_pr)
                gen_monthly_df = fetch_data_total(plant_name, ["Daily Energy"], "Inverter", current_month_start, start_date, condition_generation)
                # For poa, use plant-level average
                avg_poa = daily_poa
                monthly_poa_value = monthly_poa_solar
                # Build inverter_names from all available columns
                inverter_names = set()
                for df, suffix in [
                    (pr_df, ".PR"),
                    (gen_df, ".Daily Energy"),
                    (pr_monthly_df, ".PR"),
                    (gen_monthly_df, ".Daily Energy"),
                ]:
                    if df is not None:
                        inverter_names.update([col.replace(suffix, "") for col in df.columns if col.endswith(suffix)])
                inverter_names = sorted(inverter_names)
                records = []
                for inv in inverter_names:
                    pr = float(pr_df[f"{inv}.PR"].mean()) if pr_df is not None and f"{inv}.PR" in pr_df.columns else 0
                    generation = float(gen_df[f"{inv}.Daily Energy"].sum()) if gen_df is not None and f"{inv}.Daily Energy" in gen_df.columns else 0
                    pr_monthly = float(pr_monthly_df[f"{inv}.PR"].mean()) if pr_monthly_df is not None and f"{inv}.PR" in pr_monthly_df.columns else 0
                    generation_monthly = float(gen_monthly_df[f"{inv}.Daily Energy"].sum()) if gen_monthly_df is not None and f"{inv}.Daily Energy" in gen_monthly_df.columns else 0
                    record = {
                        "date": start_date_obj,
                        "plant_id": plant_name,
                        "plant_name": customer_name,
                        "inverter_name": inv,
                        "generation": generation,
                        "pr": pr,
                        "poa": avg_poa if avg_poa is not None else 0,
                        "generation_monthly": generation_monthly,
                        "pr_monthly": pr_monthly,
                        "poa_monthly": monthly_poa_value if monthly_poa_value is not None else 0,
                        "created_at": datetime.now(),
                        "updated_at": datetime.now()
                    }
                    records.append(record)
                if not records:
                    LOGGER.warning(f"[Solar] No inverter records built for {plant_name} on {start_date}")
                return records

            # Always process as a single plant
            inverter_records.extend(fetch_inverter_data(plant_name_solar, customer_name))
            if not inverter_records:
                LOGGER.warning(f"[Solar] No inverter records to insert for {plant_name_solar} on {start_date}")
            else:
                LOGGER.info(f"[Solar] Inverter records to insert: {inverter_records}")
                result_ids = insert_solar_inverter_data_db(inverter_records)
                if not result_ids:
                    LOGGER.error(f"[Solar] Insert returned no IDs for {plant_name_solar} on {start_date}")
                else:
                    LOGGER.info(f"[Success] Inverter-wise data inserted for {plant_name_solar} ({len(result_ids)} inverters)")
        except Exception as e:
            LOGGER.error(f"[Error] Inverter-wise data insertion failed: {e}", exc_info=True)
            # Fallback: insert zeroed-out inverter records
            try:
                # Try to get inverter names from PR or GEN dataframes
                condition_daily_pr = {"PR": "last"}
                condition_generation = {"Daily Energy": "max"}
                pr_df = fetch_data_total(plant_name_solar, ["PR"], "Inverter", start_date, start_date, condition_daily_pr)
                gen_df = fetch_data_total(plant_name_solar, ["Daily Energy"], "Inverter", start_date, start_date, condition_generation)
                inverter_names = []
                if pr_df is not None:
                    inverter_names = [col.replace(".PR", "") for col in pr_df.columns if col.endswith(".PR")]
                elif gen_df is not None:
                    inverter_names = [col.replace(".Daily Energy", "") for col in gen_df.columns if col.endswith(".Daily Energy")]
                zero_inverter_records = []
                for inv in inverter_names:
                    zero_inverter_records.append({
                        "date": start_date_obj,
                        "plant_id": plant_name_solar,
                        "plant_name": customer_name,
                        "inverter_name": inv,
                        "generation": 0,
                        "pr": 0,
                        "poa": 0,
                        "generation_monthly": 0,
                        "pr_monthly": 0,
                        "poa_monthly": 0,
                        "created_at": datetime.now(),
                        "updated_at": datetime.now()
                    })
                if zero_inverter_records:
                    LOGGER.info(f"[Solar] Fallback zero inverter records: {zero_inverter_records}")
                    insert_solar_inverter_data_db(zero_inverter_records)
                    LOGGER.info(f"[Fallback] Zero inverter data inserted for {plant_name_solar} ({len(zero_inverter_records)} inverters)")
            except Exception as e2:
                LOGGER.error(f"[DB Error] Failed inserting zero inverter data for {plant_name_solar}: {e2}", exc_info=True)

        # Cleanup
        if csv_report_path.exists():
            try:
                csv_report_path.unlink()
            except Exception as e:
                LOGGER.warning(f"Failed to clean temporary CSV: {csv_report_path} | {e}")

        return Path(summary_pdf) if not isinstance(summary_pdf, Path) else summary_pdf

    except Exception as e:
        LOGGER.error(f"[Error] Combined report generation failed: {e}", exc_info=True)
        _insert_zero_both_data(
            plant_name_solar=plant_name_solar,
            plant_name_wind=plant_name_wind,
            date=start_date,
            customer_name=customer_name,
        )
        return None


# -------------------------------
# Helpers
# -------------------------------

def _insert_zero_both_data(
    plant_name_solar: str,
    plant_name_wind: str,
    date: str,
    customer_name: str,
) -> None:
    """
    Insert a zeroed-out record into DB when combined report generation fails.
    """
    # No paired plants logic; use plant_name_solar as is

    final_pdf_path = STATIC_REPORT_DIR / f"{plant_name_solar}_DGR_{date}.jpg"
    # Fallback: parse and normalize turbine metadata for DB insertion
    turbine_metadata_str = get_turbine_metadata_from_csv(plant_name_wind)
    turbine_metadata_list = _safe_parse_metadata(turbine_metadata_str)
    csv_report_data = json.dumps(turbine_metadata_list)

    record = {
        "date": date,
        "plant_short_name_solar": plant_name_solar,
        "plant_long_name_solar": customer_name,
        "generation_solar": 0,
        "pr": 0,
        "poa": 0,
        "generation_solar_monthly": 0,
        "pr_monthly": 0,
        "poa_monthly": 0,
        "plant_short_name_wind": plant_name_wind,
        "plant_long_name_wind": customer_name,
        "generation_wind": 0,
        "wind_speed": 0,
        "generation_wind_monthly": 0,
        "wind_speed_monthly": 0,
        "approved": 0,
        "review": 0,
        "action_performed": 0,
        "dgr_path": str(final_pdf_path),
        "csv_report_data": csv_report_data,
    }
    try:
        insert_both_data_db([record])
        LOGGER.info("[Fallback] Zero combined data inserted")
    except Exception as db_e:
        LOGGER.error(f"[DB Error] Failed inserting zero combined data: {db_e}", exc_info=True)





# import json
# from pathlib import Path
# from typing import Dict, Any, List, Optional, Tuple

# import pandas as pd

# from helper.utils import (
#     get_dynamic_dates,
#     generate_combined_both_pdf,
#     fetch_data_total,
#     get_capacity_from_csv,
#     get_turbine_metadata_from_csv,
#     generate_dgr_wind_report,
#     _safe_mean,
#     _safe_sum,
# )
# from helper.logger_setup import setup_logger
# from helper.storage_s3 import upload_file_s3
# from DB.db_ops import insert_both_data_db, insert_solar_inverter_data_db, insert_wind_turbine_data_db

# # -------------------------------
# # Constants
# # -------------------------------
# STATIC_REPORT_DIR = Path("static") / "both_final_report"
# S3_REPORT_PREFIX = "both_final_report/"

# LOGGER = setup_logger("both_plants_automation", "both_plants_automation.log")


# # -------------------------------
# # Data Fetching
# # -------------------------------
# def fetch_all_solar_data(
#     plant_name: str,
#     start_date: str,
#     current_month_start: str,
#     last_30_days_start: str,
#     current_year_start: str,
#     yearly: str,
#     condition_poa: Dict[str, str],
#     condition_pr: Dict[str, str],
#     condition_generation: Dict[str, str],
#     condition_monthly_pr: Dict[str, str],
# ) -> Dict[str, pd.DataFrame]:
#     """
#     Fetch all solar data sequentially to reduce RAM usage.
#     """
#     LOGGER.info(f"[Data Fetch] Solar | Plant: {plant_name}")

#     return {
#         "poa_data": fetch_data_total(
#             plant_name, ["Daily POA Energy"], "Plant", start_date, start_date, condition_poa
#         ),
#         "pr_data": fetch_data_total(
#             plant_name, ["PR"], "Plant", start_date, start_date, condition_pr
#         ),
#         "daily_generation": fetch_data_total(
#             plant_name, ["Daily Energy"], "Plant", start_date, start_date, condition_generation
#         ),
#         "generation_monthly_value": fetch_data_total(
#             plant_name, ["Daily Energy"], "Plant", current_month_start, start_date, condition_generation
#         ),
#         "pr_data_monthly_value": fetch_data_total(
#             plant_name, ["PR"], "Plant", current_month_start, start_date, condition_monthly_pr
#         ),
#         "poa_data_monthly_value": fetch_data_total(
#             plant_name, ["Daily POA Energy"], "Plant", current_month_start, start_date, condition_poa
#         ),
#     }


# def fetch_all_wind_data(
#     plant_name: str,
#     start_date: str,
#     current_month_start: str,
#     last_30_days_start: str,
#     current_year_start: str,
#     yearly_date: str,
#     condition_wind: Dict[str, str],
#     condition_generation: Dict[str, str],
# ) -> Dict[str, pd.DataFrame]:
#     """
#     Fetch all wind data sequentially to reduce RAM usage.
#     """
#     LOGGER.info(f"[Data Fetch] Wind  | Plant: {plant_name}")

#     return {
#         "wind_speed_data_value": fetch_data_total(
#             plant_name, ["WTUR.Wind-Speed"], "Turbine", start_date, start_date, condition_wind
#         ),
#         "daily_generation_value": fetch_data_total(
#             plant_name, ["WTUR.Generation today"], "Turbine", start_date, start_date, condition_generation
#         ),
#         "wind_data_monthly_value": fetch_data_total(
#             plant_name, ["WTUR.Wind-Speed"], "Turbine", current_month_start, start_date, condition_wind
#         ),
#         "generation_monthly_value": fetch_data_total(
#             plant_name, ["WTUR.Generation today"], "Turbine", current_month_start, start_date, condition_generation
#         ),
#     }


# # -------------------------------
# # CSV & Metadata Handling
# # -------------------------------

# def _safe_parse_metadata(raw_str: Optional[str]) -> List[Dict[str, Any]]:
#     """
#     Safely parse turbine metadata JSON from CSV helper.
#     """
#     if not raw_str or raw_str == "N/A":
#         return []
#     try:
#         return json.loads(raw_str.replace('""', '"'))
#     except Exception as e:
#         LOGGER.error(f"Error parsing turbine metadata: {e}")
#         return []


# def merge_csv_with_metadata(csv_path: Path, plant_name: str) -> Optional[str]:
#     """
#     Merge CSV report data with turbine metadata and return as JSON string.
#     """
#     if not csv_path.exists():
#         LOGGER.warning(f"No CSV report found at {csv_path}")
#         return None

#     try:
#         csv_report_list = pd.read_csv(csv_path).to_dict(orient="records")

#         turbine_metadata_str = get_turbine_metadata_from_csv(plant_name)
#         turbine_metadata_list = _safe_parse_metadata(turbine_metadata_str)

#         # Index data by 'Loc No' for quick merging
#         meta_by_loc = {str(d.get("Loc No")).strip(): d for d in turbine_metadata_list}
#         csv_by_loc = {str(d.get("Loc No")).strip(): d for d in csv_report_list}

#         # Collect all keys from metadata
#         all_keys = {k for d in turbine_metadata_list for k in d}

#         merged_list = []
#         for loc_no, meta in meta_by_loc.items():
#             report_row = csv_by_loc.get(loc_no, {}).copy()
#             for k in all_keys:
#                 if k not in report_row and k in meta:
#                     report_row[k] = meta[k]
#             report_row["Loc No"] = loc_no
#             merged_list.append(report_row)

#         # Include extra CSV rows not in metadata
#         for loc_no, report_row in csv_by_loc.items():
#             if loc_no not in meta_by_loc:
#                 merged_list.append(report_row)

#         # Filter out entries where Loc No is 0 (as string or int)
#         filtered_list = [row for row in merged_list if str(row.get("Loc No", "")).strip() not in ("0", 0)]

#         return json.dumps(filtered_list)

#     except Exception as e:
#         LOGGER.error(f"Error merging CSV with metadata: {e}", exc_info=True)
#         return None


# # -------------------------------
# # KPI Computation
# # -------------------------------

# def compute_solar_kpis(
#     plant_name: str,
#     start_date: str,
# ) -> Tuple[float, float, float, float, float, float]:
#     """
#     Compute and return key solar KPIs for the given date.
#     Returns (avg_poa, daily_pr, total_generation, month_gen_value, month_pr_value, monthly_poa_value)
#     """
#     LOGGER.info(f"[Start KPIs] Solar | Plant: {plant_name}, Date: {start_date}")

#     # Hardcoded mapping for paired plants
#     paired_plants = {
#         "IN.INTE.JOD1": "IN.INTE.JOD2",
#         "IN.INTE.BAL1": "IN.INTE.BAL2",
#     }
#     paired_plant_name_bal = "Balaji Malts 1 & 2 Private Limited"
#     paired_plant_name_jod = "Jodhani 1 & 2 Papers Pvt Ltd"

#     # If plant_name is a key in paired_plants, fetch both and sum
#     if plant_name in paired_plants:
#         plant_1 = plant_name
#         plant_2 = paired_plants[plant_name]

#         # Fetch KPIs for both plants
#         kpis_1 = _compute_solar_kpis_single(plant_1, start_date)
#         kpis_2 = _compute_solar_kpis_single(plant_2, start_date)

#         # Sum/average as appropriate
#         avg_poa = kpis_1[0] + kpis_2[0]
#         daily_pr = (kpis_1[1] + kpis_2[1]) / 2 if (kpis_1[1] is not None and kpis_2[1] is not None) else None
#         total_generation = kpis_1[2] + kpis_2[2]
#         month_gen_value = kpis_1[3] + kpis_2[3]
#         month_pr_value = (kpis_1[4] + kpis_2[4]) / 2 if (kpis_1[4] is not None and kpis_2[4] is not None) else None
#         monthly_poa_value = kpis_1[5] + kpis_2[5]
#         return avg_poa, daily_pr, total_generation, month_gen_value, month_pr_value, monthly_poa_value
#     else:
#         return _compute_solar_kpis_single(plant_name, start_date)

# def _compute_solar_kpis_single(
#     plant_name: str,
#     start_date: str,
# ) -> Tuple[float, float, float, float, float, float]:
#     """
#     Compute KPIs for a single solar plant (helper for hardcoded mapping logic).
#     """
#     # Define fetch conditions
#     condition_poa = {"Daily POA Energy": "last"}
#     condition_daily_pr = {"PR": "last"}
#     condition_monthly_pr = {"PR": "mean"}
#     condition_generation = {"Daily Energy": "max"}

#     # Get date ranges
#     current_month_start, _, _, _, _, last_30_days_start, current_year_start, last_year_date = get_dynamic_dates(start_date)

#     # Fetch all required data
#     data = fetch_all_solar_data(
#         plant_name,
#         start_date,
#         current_month_start,
#         last_30_days_start,
#         current_year_start,
#         last_year_date,
#         condition_poa,
#         condition_daily_pr,
#         condition_generation,
#         condition_monthly_pr,
#     )

#     # Extract and compute key metrics
#     total_generation = _safe_sum(data["daily_generation"])  # kWh/MWh as per source
#     month_gen_value = _safe_sum(data["generation_monthly_value"])  # monthly gen sum
#     month_pr_value = _safe_mean(data["pr_data_monthly_value"])  # monthly PR mean
#     daily_pr_percentage = _safe_mean(data["pr_data"])  # today's PR
#     monthly_poa_value = _safe_mean(data["poa_data_monthly_value"])  # monthly POA mean
#     avg_poa = _safe_mean(data["poa_data"])  # today's POA

#     return avg_poa, daily_pr_percentage, total_generation, month_gen_value, month_pr_value, monthly_poa_value


# def compute_wind_kpis(
#     plant_name: str,
#     start_date: str,
#     ma_percent: float,
# ) -> Tuple[float, float, float, float, pd.DataFrame, pd.DataFrame]:
#     """
#     Compute and return key wind KPIs and raw frames needed for CSV.
#     Returns (avg_wind_speed, total_generation, monthly_wind, monthly_generation, wind_speed_df, daily_generation_df)
#     """
#     LOGGER.info(f"[Start KPIs] Wind  | Plant: {plant_name}, Date: {start_date}")

#     current_month_start, _, _, _, _, last_30_days_start, current_year_start, last_year_date = get_dynamic_dates(start_date)
#     condition_wind = {"Wind-Speed": "mean"}
#     condition_generation = {"Generation today": "last"}

#     data = fetch_all_wind_data(
#         plant_name,
#         start_date,
#         current_month_start,
#         last_30_days_start,
#         current_year_start,
#         last_year_date,
#         condition_wind,
#         condition_generation,
#     )

#     avg_wind_speed = _safe_mean(data["wind_speed_data_value"])  # today's wind speed
#     total_generation = _safe_sum(data["daily_generation_value"])  # today's generation
#     monthly_wind = _safe_mean(data["wind_data_monthly_value"])  # monthly wind mean
#     monthly_generation = _safe_sum(data["generation_monthly_value"])  # monthly generation sum

#     return (
#         avg_wind_speed,
#         total_generation,
#         monthly_wind,
#         monthly_generation,
#         data["wind_speed_data_value"],
#         data["daily_generation_value"],
#     )


# # -------------------------------
# # Main Combined Report Generation
# # -------------------------------

# def combined_both_plants(
#     plant_name_solar: str,
#     plant_name_wind: str,
#     start_date: str,
#     customer_name: str,
#     project: str,
#     ma_percent: float,
# ) -> Optional[Path]:
#     """
#     Enhanced: Ensures correct types for DB, logs all records, and warns on empty data.
#     """
#     from datetime import datetime, date
#     # Convert start_date to date object if string
#     if isinstance(start_date, str):
#         try:
#             start_date_obj = datetime.strptime(start_date, "%Y-%m-%d").date()
#         except Exception:
#             try:
#                 start_date_obj = datetime.strptime(start_date, "%Y/%m/%d").date()
#             except Exception:
#                 LOGGER.error(f"Could not parse start_date: {start_date}")
#                 start_date_obj = start_date
#     else:
#         start_date_obj = start_date
#     """
#     Generates a combined report for both solar and wind plants and stores it to S3 and DB.
#     Returns the path to the generated combined report, or None on failure.
#     """
#     # Handle paired plants for solar: use combined name if in paired_plants
#     paired_plants = {
#         "IN.INTE.JOD1": "IN.INTE.JOD2",
#         "IN.INTE.BAL1": "IN.INTE.BAL2",
#     }

#     paired_plant_name_bal = "Balaji Malts 1 & 2 Private Limited"
#     paired_plant_name_jod = "Jodhani 1 & 2 Papers Pvt Ltd"

#     if plant_name_solar in paired_plants:
#         plant_name_solar = f"{plant_name_solar}&{paired_plants[plant_name_solar]}"

#     LOGGER.info(
#         f"[Start Report] Both | Solar: {plant_name_solar}, Wind: {plant_name_wind}, Date: {start_date}"
#     )

#     try:
#         # Compute KPIs for both plants
#         (
#             daily_poa,
#             daily_pr,
#             daily_gen_solar,
#             month_gen_solar,
#             month_pr_value,
#             monthly_poa_solar,
#         ) = compute_solar_kpis(plant_name_solar, start_date)

#         (
#             daily_wind_speed,
#             daily_gen_wind,
#             monthly_wind,
#             monthly_gen_wind,
#             wind_speed_df,
#             daily_generation_df,
#         ) = compute_wind_kpis(plant_name_wind, start_date, ma_percent)

#         # Capacities
#         capacity_solar = get_capacity_from_csv(plant_name_solar)
#         capacity_wind = get_capacity_from_csv(plant_name_wind)

#         # Paths
#         STATIC_REPORT_DIR.mkdir(parents=True, exist_ok=True)
#         final_pdf_path = STATIC_REPORT_DIR / f"{plant_name_solar}_DGR_{start_date}.jpg"

#         # Wind CSV report for table merge on combined sheet
#         csv_report_path_str = generate_dgr_wind_report(
#             plant_name_wind,
#             wind_speed_df,
#             daily_generation_df,
#             start_date,
#             customer_name,
#             project,
#             ma_percent,
#         )
#         csv_report_path = Path(csv_report_path_str)

#         # Generate combined PDF
#         summary_pdf = generate_combined_both_pdf(
#             start_date,
#             customer_name,
#             project,
#             daily_poa,
#             daily_pr,
#             daily_gen_solar,
#             month_gen_solar,
#             month_pr_value,
#             monthly_poa_solar,
#             daily_wind_speed,
#             daily_gen_wind,
#             monthly_wind,
#             monthly_gen_wind,
#             capacity_wind,
#             capacity_solar,
#             csv_report_path_str,
#             plant_name_solar,
#             plant_name_wind,
#             final_pdf_path,
#         )

#         # Upload to S3
#         s3_relative_path = f"{S3_REPORT_PREFIX}{plant_name_solar}_DGR_{start_date}.jpg"
#         upload_file_s3(summary_pdf, s3_relative_path)

#         # Prepare DB record
#         # Set correct customer name for paired plants
#         if plant_name_solar == "IN.INTE.BAL1&IN.INTE.BAL2":
#             plant_long_name_solar_final = paired_plant_name_bal
#         elif plant_name_solar == "IN.INTE.JOD1&IN.INTE.JOD2":
#             plant_long_name_solar_final = paired_plant_name_jod
#         else:
#             plant_long_name_solar_final = customer_name

#         record = {
#             "date": start_date,
#             "plant_short_name_solar": plant_name_solar,
#             "plant_long_name_solar": plant_long_name_solar_final,
#             "generation_solar": round(float(daily_gen_solar), 2),
#             "pr": round(float(daily_pr), 2),
#             "poa": round(float(daily_poa), 2),
#             "generation_solar_monthly": round(float(month_gen_solar), 2),
#             "pr_monthly": round(float(month_pr_value), 2),
#             "poa_monthly": round(float(monthly_poa_solar), 2),
#             "plant_short_name_wind": plant_name_wind,
#             "plant_long_name_wind": customer_name,
#             "generation_wind": round(float(daily_gen_wind), 2),
#             "wind_speed": round(float(daily_wind_speed), 2),
#             "generation_wind_monthly": round(float(monthly_gen_wind), 2),
#             "wind_speed_monthly": round(float(monthly_wind), 2),
#             "approved": 0,
#             "review": 0,
#             "action_performed": 0,
#             "dgr_path": str(summary_pdf),
#         }

#         # Merge CSV with metadata and add as JSON string to data (like wind_automation.py)
#         record["csv_report_data"] = merge_csv_with_metadata(csv_report_path, plant_name_wind)
#         record["edit_csv_report_data"] = None

#         insert_both_data_db([record])
#         LOGGER.info(
#             f"[Success] Both report generated + stored | Solar: {plant_name_solar}, Wind: {plant_name_wind}"
#         )

#         # --- Turbine-wise wind data insertion (like wind_automation.py) ---
#         try:
#             import pandas as pd
#             # Read turbine metadata
#             turbine_metadata_str = get_turbine_metadata_from_csv(plant_name_wind)
#             turbine_metadata_list = _safe_parse_metadata(turbine_metadata_str)
#             # Read CSV as dict indexed by Loc No
#             df_turbine = pd.read_csv(csv_report_path)
#             csv_by_loc = {str(row.get("Loc No")).strip(): row for _, row in df_turbine.iterrows()}
#             turbine_records = []
#             # Fetch monthly wind data for turbines
#             current_month_start, _, _, _, _, last_30_days_start, current_year_start, last_year_date = get_dynamic_dates(start_date)
#             condition_wind = {"Wind-Speed": "mean"}
#             condition_generation = {"Generation today": "last"}
#             wind_monthly_data = fetch_all_wind_data(
#                 plant_name_wind,
#                 start_date,
#                 current_month_start,
#                 last_30_days_start,
#                 current_year_start,
#                 last_year_date,
#                 condition_wind,
#                 condition_generation,
#             )
#             gen_monthly_df = wind_monthly_data.get("generation_monthly_value")
#             wind_monthly_df = wind_monthly_data.get("wind_data_monthly_value")
#             for meta in turbine_metadata_list:
#                 loc_no = str(meta.get("Loc No", "0")).strip()
#                 csv_row = csv_by_loc.get(loc_no, {})
#                 # Use daily values from CSV if present, else 0
#                 generation = float(csv_row.get("Daily Generation (KWh)", 0))
#                 avg_wind_speed = float(csv_row.get("Avg Wind Speed", 0))
#                 # Calculate monthly values for this turbine
#                 gen_monthly = None
#                 wind_monthly = None
#                 if gen_monthly_df is not None and f"{loc_no}.Generation today" in gen_monthly_df.columns:
#                     gen_monthly = float(gen_monthly_df[f"{loc_no}.Generation today"].sum())
#                 if wind_monthly_df is not None and f"{loc_no}.Wind-Speed" in wind_monthly_df.columns:
#                     wind_monthly = float(wind_monthly_df[f"{loc_no}.Wind-Speed"].mean())
#                 turbine_record = {
#                     "date": start_date_obj,
#                     "plant_id": plant_name_wind,
#                     "plant_name": customer_name,
#                     "turbine_name": loc_no,
#                     "generation": generation,
#                     "avg_wind_speed": avg_wind_speed,
#                     "generation_monthly": gen_monthly,
#                     "avg_wind_speed_monthly": wind_monthly,
#                     "created_at": datetime.now(),
#                     "updated_at": datetime.now()
#                 }
#                 # Optionally, add more fields from metadata if needed
#                 turbine_records.append(turbine_record)
#             if not turbine_records:
#                 LOGGER.warning(f"[Wind] No turbine records built for {plant_name_wind} on {start_date}")
#             else:
#                 LOGGER.info(f"[Wind] Turbine records to insert: {turbine_records}")
#                 result_ids = insert_wind_turbine_data_db(turbine_records)
#                 if not result_ids:
#                     LOGGER.error(f"[Wind] Insert returned no IDs for {plant_name_wind} on {start_date}")
#                 else:
#                     LOGGER.info(f"[Success] Turbine-wise data inserted for {plant_name_wind} ({len(result_ids)} turbines)")
#         except Exception as e:
#             LOGGER.error(f"[Error] Turbine-wise data insertion failed: {e}", exc_info=True)
#             # Fallback: insert zeroed-out turbine records
#             try:
#                 turbine_metadata_str = get_turbine_metadata_from_csv(plant_name_wind)
#                 turbine_metadata_list = _safe_parse_metadata(turbine_metadata_str)
#                 zero_turbine_records = []
#                 for meta in turbine_metadata_list:
#                     loc_no = str(meta.get("Loc No", "0"))
#                     zero_turbine_records.append({
#                         "date": start_date_obj,
#                         "plant_id": plant_name_wind,
#                         "plant_name": customer_name,
#                         "turbine_name": loc_no,
#                         "generation": 0,
#                         "avg_wind_speed": 0,
#                         "generation_monthly": 0,
#                         "avg_wind_speed_monthly": 0,
#                         "created_at": datetime.now(),
#                         "updated_at": datetime.now()
#                     })
#                 if zero_turbine_records:
#                     LOGGER.info(f"[Wind] Fallback zero turbine records: {zero_turbine_records}")
#                     insert_wind_turbine_data_db(zero_turbine_records)
#                     LOGGER.info(f"[Fallback] Zero turbine data inserted for {plant_name_wind} ({len(zero_turbine_records)} turbines)")
#             except Exception as e2:
#                 LOGGER.error(f"[DB Error] Failed inserting zero turbine data for {plant_name_wind}: {e2}", exc_info=True)

#         # --- Inverter-wise solar data insertion (like solar_automation.py) ---
#         try:
#             # For paired plants, need to handle each plant separately
#             inverter_records = []
#             # Helper to fetch inverter-wise data for a plant
#             def fetch_inverter_data(plant_name, customer_name):
#                 # Fetch inverter-wise dataframes
#                 condition_poa = {"Daily POA Energy": "last"}
#                 condition_daily_pr = {"PR": "last"}
#                 condition_monthly_pr = {"PR": "mean"}
#                 condition_generation = {"Daily Energy": "max"}
#                 current_month_start, _, _, _, _, last_30_days_start, current_year_start, last_year_date = get_dynamic_dates(start_date)
#                 pr_df = fetch_data_total(plant_name, ["PR"], "Inverter", start_date, start_date, condition_daily_pr)
#                 gen_df = fetch_data_total(plant_name, ["Daily Energy"], "Inverter", start_date, start_date, condition_generation)
#                 pr_monthly_df = fetch_data_total(plant_name, ["PR"], "Inverter", current_month_start, start_date, condition_monthly_pr)
#                 gen_monthly_df = fetch_data_total(plant_name, ["Daily Energy"], "Inverter", current_month_start, start_date, condition_generation)
#                 # For poa, use plant-level average
#                 avg_poa = None
#                 monthly_poa_value = None
#                 avg_poa = daily_poa if plant_name in plant_name_solar else None
#                 monthly_poa_value = monthly_poa_solar if plant_name in plant_name_solar else None
#                 # Build inverter_names from all available columns
#                 inverter_names = set()
#                 for df, suffix in [
#                     (pr_df, ".PR"),
#                     (gen_df, ".Daily Energy"),
#                     (pr_monthly_df, ".PR"),
#                     (gen_monthly_df, ".Daily Energy"),
#                 ]:
#                     if df is not None:
#                         inverter_names.update([col.replace(suffix, "") for col in df.columns if col.endswith(suffix)])
#                 inverter_names = sorted(inverter_names)
#                 records = []
#                 for inv in inverter_names:
#                     pr = float(pr_df[f"{inv}.PR"].mean()) if pr_df is not None and f"{inv}.PR" in pr_df.columns else 0
#                     generation = float(gen_df[f"{inv}.Daily Energy"].sum()) if gen_df is not None and f"{inv}.Daily Energy" in gen_df.columns else 0
#                     pr_monthly = float(pr_monthly_df[f"{inv}.PR"].mean()) if pr_monthly_df is not None and f"{inv}.PR" in pr_monthly_df.columns else 0
#                     generation_monthly = float(gen_monthly_df[f"{inv}.Daily Energy"].sum()) if gen_monthly_df is not None and f"{inv}.Daily Energy" in gen_monthly_df.columns else 0
#                     record = {
#                         "date": start_date_obj,
#                         "plant_id": plant_name,
#                         "plant_name": customer_name,
#                         "inverter_name": inv,
#                         "generation": generation,
#                         "pr": pr,
#                         "poa": avg_poa if avg_poa is not None else 0,
#                         "generation_monthly": generation_monthly,
#                         "pr_monthly": pr_monthly,
#                         "poa_monthly": monthly_poa_value if monthly_poa_value is not None else 0,
#                         "created_at": datetime.now(),
#                         "updated_at": datetime.now()
#                     }
#                     records.append(record)
#                 if not records:
#                     LOGGER.warning(f"[Solar] No inverter records built for {plant_name} on {start_date}")
#                 return records

#             # Handle paired plants (split by &)
#             if "&" in plant_name_solar:
#                 plants = plant_name_solar.split("&")
#                 for p in plants:
#                     inverter_records.extend(fetch_inverter_data(p.strip(), customer_name))
#             else:
#                 inverter_records.extend(fetch_inverter_data(plant_name_solar, customer_name))
#             if not inverter_records:
#                 LOGGER.warning(f"[Solar] No inverter records to insert for {plant_name_solar} on {start_date}")
#             else:
#                 LOGGER.info(f"[Solar] Inverter records to insert: {inverter_records}")
#                 result_ids = insert_solar_inverter_data_db(inverter_records)
#                 if not result_ids:
#                     LOGGER.error(f"[Solar] Insert returned no IDs for {plant_name_solar} on {start_date}")
#                 else:
#                     LOGGER.info(f"[Success] Inverter-wise data inserted for {plant_name_solar} ({len(result_ids)} inverters)")
#         except Exception as e:
#             LOGGER.error(f"[Error] Inverter-wise data insertion failed: {e}", exc_info=True)
#             # Fallback: insert zeroed-out inverter records
#             try:
#                 # Try to get inverter names from PR or GEN dataframes
#                 condition_daily_pr = {"PR": "last"}
#                 condition_generation = {"Daily Energy": "max"}
#                 pr_df = fetch_data_total(plant_name_solar, ["PR"], "Inverter", start_date, start_date, condition_daily_pr)
#                 gen_df = fetch_data_total(plant_name_solar, ["Daily Energy"], "Inverter", start_date, start_date, condition_generation)
#                 inverter_names = []
#                 if pr_df is not None:
#                     inverter_names = [col.replace(".PR", "") for col in pr_df.columns if col.endswith(".PR")]
#                 elif gen_df is not None:
#                     inverter_names = [col.replace(".Daily Energy", "") for col in gen_df.columns if col.endswith(".Daily Energy")]
#                 zero_inverter_records = []
#                 for inv in inverter_names:
#                     zero_inverter_records.append({
#                         "date": start_date_obj,
#                         "plant_id": plant_name_solar,
#                         "plant_name": customer_name,
#                         "inverter_name": inv,
#                         "generation": 0,
#                         "pr": 0,
#                         "poa": 0,
#                         "generation_monthly": 0,
#                         "pr_monthly": 0,
#                         "poa_monthly": 0,
#                         "created_at": datetime.now(),
#                         "updated_at": datetime.now()
#                     })
#                 if zero_inverter_records:
#                     LOGGER.info(f"[Solar] Fallback zero inverter records: {zero_inverter_records}")
#                     insert_solar_inverter_data_db(zero_inverter_records)
#                     LOGGER.info(f"[Fallback] Zero inverter data inserted for {plant_name_solar} ({len(zero_inverter_records)} inverters)")
#             except Exception as e2:
#                 LOGGER.error(f"[DB Error] Failed inserting zero inverter data for {plant_name_solar}: {e2}", exc_info=True)

#         # Cleanup
#         if csv_report_path.exists():
#             try:
#                 csv_report_path.unlink()
#             except Exception as e:
#                 LOGGER.warning(f"Failed to clean temporary CSV: {csv_report_path} | {e}")

#         return Path(summary_pdf) if not isinstance(summary_pdf, Path) else summary_pdf

#     except Exception as e:
#         LOGGER.error(f"[Error] Combined report generation failed: {e}", exc_info=True)
#         _insert_zero_both_data(
#             plant_name_solar=plant_name_solar,
#             plant_name_wind=plant_name_wind,
#             date=start_date,
#             customer_name=customer_name,
#         )
#         return None


# # -------------------------------
# # Helpers
# # -------------------------------

# def _insert_zero_both_data(
#     plant_name_solar: str,
#     plant_name_wind: str,
#     date: str,
#     customer_name: str,
# ) -> None:
#     """
#     Insert a zeroed-out record into DB when combined report generation fails.
#     """
#     # Handle paired plants for solar: use combined name if in paired_plants
#     paired_plants = {
#         "IN.INTE.JOD1": "IN.INTE.JOD2",
#         "IN.INTE.BAL1": "IN.INTE.BAL2",
#     }
#     if plant_name_solar in paired_plants:
#         plant_name_solar = f"{plant_name_solar}&{paired_plants[plant_name_solar]}"

#     final_pdf_path = STATIC_REPORT_DIR / f"{plant_name_solar}_DGR_{date}.jpg"
#     # Fallback: parse and normalize turbine metadata for DB insertion
#     turbine_metadata_str = get_turbine_metadata_from_csv(plant_name_wind)
#     turbine_metadata_list = _safe_parse_metadata(turbine_metadata_str)
#     csv_report_data = json.dumps(turbine_metadata_list)

#     record = {
#         "date": date,
#         "plant_short_name_solar": plant_name_solar,
#         "plant_long_name_solar": customer_name,
#         "generation_solar": 0,
#         "pr": 0,
#         "poa": 0,
#         "generation_solar_monthly": 0,
#         "pr_monthly": 0,
#         "poa_monthly": 0,
#         "plant_short_name_wind": plant_name_wind,
#         "plant_long_name_wind": customer_name,
#         "generation_wind": 0,
#         "wind_speed": 0,
#         "generation_wind_monthly": 0,
#         "wind_speed_monthly": 0,
#         "approved": 0,
#         "review": 0,
#         "action_performed": 0,
#         "dgr_path": str(final_pdf_path),
#         "csv_report_data": csv_report_data,
#     }
#     try:
#         insert_both_data_db([record])
#         LOGGER.info("[Fallback] Zero combined data inserted")
#     except Exception as db_e:
#         LOGGER.error(f"[DB Error] Failed inserting zero combined data: {db_e}", exc_info=True)
