import pandas as pd
import time
import csv
from config.settings import Config
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, PageBreak
from helper.integration_utilities import PrescintoIntegrationUtilities
from PyPDF2 import PdfMerger
from helper.logger_setup import setup_logger
import os
import traceback
from dotenv import load_dotenv
from datetime import datetime, timedelta
from collections import defaultdict
from dateutil.relativedelta import relativedelta
import re



load_dotenv()

# Setup logger
logging = setup_logger('utils', 'utils.log')


PRESCINTO_TOKEN = os.getenv('PRESCINTO_TOKEN')

# Initialize Prescinto Integration
PRESCINTO_API = PrescintoIntegrationUtilities(server='IN', token=PRESCINTO_TOKEN)

def fetch_data(plant_name, params, category, start_date, end_date, retries=3):
    """
    Fetches data from Prescinto API with error handling and retries.
    """
    for attempt in range(retries):
        try:
            data = PRESCINTO_API.fetchDataV2(plant_name, category, params, None, start_date, end_date, granularity='15m')
            if isinstance(data, str):  # API returned an error message as a string
                logging.error(f"Error fetching {params} (Attempt {attempt+1}): {data}")
                continue
            df = pd.DataFrame(data)
            if not df.empty:
                return df
        except Exception as e:
            logging.error(f"Exception fetching {params} (Attempt {attempt+1}): {e}")
        time.sleep(5)  # Wait before retrying
    return pd.DataFrame()


def generate_solar_dgr_report(poa, pr, total_generation, start_date, customer_name, project):
    # Ensure missing values are replaced with zero
    poa = poa if poa not in [None, ""] else 0
    pr = pr if pr not in [None, ""] else 0
    total_generation = total_generation if total_generation not in [None, ""] else 0

    output_file = f"Solar_DGR_Report_{start_date}.csv"
    
    try:
        with open(output_file, mode="w", newline="") as file:
            writer = csv.writer(file)
            writer.writerow(["Date", "Customer Name", "Project", "POA(w/m2)", "PR%", "Daily Generation(kwh)"])
            writer.writerow([start_date, customer_name, project, round(float(poa), 2), round(float(pr), 2), round(float(total_generation), 2)])
        
        return output_file

    except Exception as e:
        logging.error(f"Error generating CSV report: {e}")
        return None

import os
from PIL import Image, ImageDraw, ImageFont
import pandas as pd

def wrap_text(text, font, max_width, draw):
    words = text.split()
    lines, line = [], ''
    for word in words:
        test = f'{line} {word}'.strip()
        width = draw.textlength(test, font=font)
        if width <= max_width:
            line = test
        else:
            if line:
                lines.append(line)
            line = word
    if line:
        lines.append(line)
    return lines

def center_table_horiz(image_width, table_width):
    """Compute left margin for horizontal centering."""
    return (image_width - table_width) // 2

def draw_comment_below_box(draw, left, width, top, comment_text, font):
    """Draw red comment text below the box/table."""
    max_width = width - 10
    wrapped = wrap_text(comment_text, font, max_width, draw)
    y = top + 10
    for line in wrapped:
        draw.text((left + 5, y), line, fill='red', font=font)
        y += font.getbbox(line)[3] - font.getbbox(line)[1] + 4

def generate_combined_wind_pdf(
    plant_name, start_date, customer_name, project,
    avg_wind_speed, total_generation, ma_percent,
    monthly_wind, monthly_generation, csv_filename, capacity,
    output_file, comment_text=None
):
    width, height = 1100, 800
    image = Image.new("RGB", (width, height), "white")
    draw = ImageDraw.Draw(image)

    try:
        title_font = ImageFont.truetype("arialbd.ttf", 20)
        label_font = ImageFont.truetype("arialbd.ttf", 14)
        value_font = ImageFont.truetype("arial.ttf", 14)
        table_header_font = ImageFont.truetype("arialbd.ttf", 13)
        table_font = ImageFont.truetype("arial.ttf", 13)
        comment_font = ImageFont.truetype("arial.ttf", 13)
    except OSError:
        title_font = label_font = value_font = table_font = table_header_font = comment_font = ImageFont.load_default()

    # Title
    title = f"{customer_name} - DAILY GENERATION REPORT\nCapacity: {capacity} MW | Date: {start_date}"
    max_title_width = width - 40
    title_lines = []
    for part in title.split('\n'):
        title_lines.extend(wrap_text(part, title_font, max_title_width, draw))
    y = 20
    for line in title_lines:
        draw.text((20, y), line, fill="black", font=title_font)
        bbox = title_font.getbbox(line)
        line_height = bbox[3] - bbox[1] if bbox else 24
        y += line_height + 4
    table_top = y + 8

    # Summary Table Data (centered)
    summary_data = [
        ("Daily Avg Wind Speed (m/s):", round(float(avg_wind_speed), 2)),
        ("Daily Generation (kWh):", round(float(total_generation))),
        ("Monthly Wind Speed (m/s):", round(float(monthly_wind), 2)),
        ("Monthly Generation (kWh):", round(float(monthly_generation))),
    ]
    row_height = 40
    col1_width, col2_width = 230, 300
    table_width = col1_width + col2_width
    table_left = center_table_horiz(width, table_width)
    label_bg = (220, 220, 220)
    box_color = "black"

    for i, (label, value) in enumerate(summary_data):
        row_y = table_top + i * row_height
        draw.rectangle([table_left, row_y, table_left + col1_width, row_y + row_height], fill=label_bg)
        draw.rectangle([table_left + col1_width, row_y, table_left + col1_width + col2_width, row_y + row_height], outline=box_color, width=2)
        # Centered text in both columns
        label_x = table_left + (col1_width - draw.textlength(label, font=label_font)) // 2
        value_x = table_left + col1_width + (col2_width - draw.textlength(str(value), font=value_font)) // 2
        draw.text((label_x, row_y + 10), label, fill="black", font=label_font)
        draw.text((value_x, row_y + 10), str(value), fill="black", font=value_font)

    # Comment below table if present
    if comment_text:
        comment_top = table_top + len(summary_data)*row_height
        draw_comment_below_box(draw, table_left, table_width, comment_top, comment_text, comment_font)

    y_csv = table_top + len(summary_data) * row_height + (130 if comment_text else 20)
    if csv_filename and os.path.exists(csv_filename):
        df = pd.read_csv(csv_filename)
        if not df.empty:
            data = [df.columns.tolist()] + df.values.tolist()
            n_cols = len(df.columns)
            
            # Match the summary table width
            target_table_width = table_width  # Use same width as summary table (530px)
            cell_w = target_table_width // n_cols
            x0 = table_left  # Use same left position as summary table
            
            # Draw headers
            for j, header in enumerate(data[0]):
                cell_x = x0 + j * cell_w
                draw.rectangle([cell_x, y_csv, cell_x + cell_w, y_csv + row_height], fill=(44, 62, 80))
                
                # Wrap and center header text if needed
                header_text = str(header)
                if draw.textlength(header_text, font=table_header_font) > cell_w - 16:
                    header_lines = wrap_text(header_text, table_header_font, cell_w - 16, draw)
                    for idx, line in enumerate(header_lines[:2]):  # Limit to 2 lines
                        text_x = cell_x + (cell_w - draw.textlength(line, font=table_header_font)) // 2
                        draw.text((text_x, y_csv + 4 + idx * 12), line, fill="white", font=table_header_font)
                else:
                    text_x = cell_x + (cell_w - draw.textlength(header_text, font=table_header_font)) // 2
                    draw.text((text_x, y_csv + 8), header_text, fill="white", font=table_header_font)
                    
                draw.rectangle([cell_x, y_csv, cell_x + cell_w, y_csv + row_height], outline="grey", width=1)

            # Draw data rows
            max_rows = (height - y_csv - 50) // row_height
            for i, row in enumerate(data[1:max_rows]):
                row_y = y_csv + (i + 1) * row_height
                row_bg = (236, 240, 241) if i % 2 == 0 else (255, 255, 255)
                for j, value in enumerate(row):
                    cell_x = x0 + j * cell_w
                    draw.rectangle([cell_x, row_y, cell_x + cell_w, row_y + row_height], fill=row_bg)
                    
                    # Center the cell content
                    value_text = str(value)
                    if draw.textlength(value_text, font=table_font) > cell_w - 16:
                        # Truncate if too long
                        while draw.textlength(value_text + "...", font=table_font) > cell_w - 16 and len(value_text) > 3:
                            value_text = value_text[:-1]
                        value_text += "..." if len(str(value)) > len(value_text) else ""
                    
                    text_x = cell_x + (cell_w - draw.textlength(value_text, font=table_font)) // 2
                    draw.text((text_x, row_y + 8), value_text, fill="black", font=table_font)
                    draw.rectangle([cell_x, row_y, cell_x + cell_w, row_y + row_height], outline="grey", width=1)


    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    image.save(output_file, "JPEG")
    return output_file


def generate_solar_dgr_pdf(
    date, customer_name, poa, pr, daily_generation, month_gen_value, month_pr_value,
    monthly_poa, capacity, output_file, comment_text=None
):
    width, height = 800, 500
    image = Image.new("RGB", (width, height), "white")
    draw = ImageDraw.Draw(image)

    try:
        title_font = ImageFont.truetype("arialbd.ttf", 20)
        label_font = ImageFont.truetype("arialbd.ttf", 14)
        value_font = ImageFont.truetype("arial.ttf", 14)
        comment_font = ImageFont.truetype("arial.ttf", 13)
    except OSError:
        title_font = label_font = value_font = comment_font = ImageFont.load_default()

    # Compose and draw title
    title = f"{customer_name} - DAILY GENERATION REPORT\nCapacity: {capacity} MW | Date: {date}"
    max_title_width = width - 40
    title_lines = []
    for part in title.split('\n'):
        title_lines.extend(wrap_text(part, title_font, max_title_width, draw))
    y = 20
    for line in title_lines:
        draw.text((20, y), line, fill="black", font=title_font)
        line_height = title_font.getbbox(line)[3] - title_font.getbbox(line)[1]
        y += line_height + 4

    # Data rows (centered)
    data = [
        ("Daily POA (kWh/m2):", round(float(poa / 1000), 2)),
        ("Daily PR%:", round(float(pr), 1)),
        ("Daily Generation (kWh):", round(float(daily_generation))),
        ("Monthly PR%:", round(month_pr_value, 1)),
        ("Monthly Generation (kWh):", round(float(month_gen_value))),
        ("Monthly POA (kWh/m2):", round(float(monthly_poa / 1000), 2))
    ]
    row_height = 40
    col1_width, col2_width = 220, 250
    table_width = col1_width + col2_width
    table_left = center_table_horiz(width, table_width)
    table_top = y + 8
    label_bg = (220, 220, 220)
    box_color = "black"

    for i, (label, value) in enumerate(data):
        row_y = table_top + i * row_height
        draw.rectangle([table_left, row_y, table_left + col1_width, row_y + row_height], fill=label_bg)
        draw.rectangle([table_left + col1_width, row_y, table_left + table_width, row_y + row_height], outline=box_color, width=2)
        # Center text in both columns
        label_x = table_left + (col1_width - draw.textlength(label, font=label_font)) // 2
        value_x = table_left + col1_width + (col2_width - draw.textlength(str(value), font=value_font)) // 2
        draw.text((label_x, row_y + 10), label, fill="black", font=label_font)
        draw.text((value_x, row_y + 10), str(value), fill="black", font=value_font)

    # Draw comment below table (centered, red)
    if comment_text:
        comment_top = table_top + len(data)*row_height
        draw_comment_below_box(draw, table_left, table_width, comment_top, comment_text, comment_font)

    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    image.save(output_file, "JPEG")
    return output_file


import os
import pandas as pd
from PIL import Image, ImageDraw, ImageFont

def center_table_horiz(page_width, table_width):
    return (page_width - table_width) // 2

def wrap_text(text, font, max_width, draw):
    lines = []
    current_line = ""
    for word in text.split():
        test_line = f"{current_line} {word}".strip()
        if draw.textlength(test_line, font=font) <= max_width:
            current_line = test_line
        else:
            lines.append(current_line)
            current_line = word
    lines.append(current_line)
    return lines

def draw_comment_box(draw, x, y, width, text, font):
    draw.rectangle([x, y, x + width, y + 60], outline="gray", width=1)
    wrapped = wrap_text(text, font, width - 10, draw)
    for i, line in enumerate(wrapped[:2]):  # Max 2 lines
        draw.text((x + 5, y + 5 + i * 22), line, fill="black", font=font)  # increased line spacing

def generate_combined_both_pdf(
    date, customer_name, project,
    daily_poa, daily_pr, daily_gen_solar, month_gen_solar, month_pr_solar,
    monthly_poa_solar, daily_wind_speed, daily_gen_wind, monthly_wind, monthly_gen_wind,
    wind_capacity, solar_capacity, csv_filename,
    plant_name_solar, plant_name_wind, output_file, comment_text=None
):
    width, height = 1100, 1500
    image = Image.new("RGB", (width, height), "white")
    draw = ImageDraw.Draw(image)

    # Increased font sizes
    try:
        title_font = ImageFont.truetype("arialbd.ttf", 28)   # was 22
        heading_font = ImageFont.truetype("arialbd.ttf", 24) # was 18
        label_font = ImageFont.truetype("arialbd.ttf", 18)   # was 14
        value_font = ImageFont.truetype("arial.ttf", 18)     # was 14
        table_font = ImageFont.truetype("arial.ttf", 17)     # was 13
        comment_font = ImageFont.truetype("arial.ttf", 17)   # was 13
    except OSError:
        title_font = heading_font = label_font = value_font = table_font = comment_font = ImageFont.load_default()

    y = 20
    title = f"{customer_name} - DAILY GENERATION REPORT\n| Date: {date}"
    for part in title.split("\n"):
        for line in wrap_text(part, title_font, width - 40, draw):
            draw.text((20, y), line, fill="black", font=title_font)
            y += 36  # was 28
    y += 15

    if comment_text:
        comment_width = 800
        comment_left = center_table_horiz(width, comment_width)
        draw_comment_box(draw, comment_left, y, comment_width, comment_text, comment_font)
        y += 74  # was 64

    # Solar Data
    draw.text((20, y), f"Solar Generation: {plant_name_solar}", fill="black", font=heading_font)
    y += 40  # was 32
    solar_data = [
        ("Solar Plant Capacity (MW):", solar_capacity),
        ("Daily POA (kW/hm2):", round(daily_poa / 1000, 2)),
        ("Daily PR%:", round(daily_pr, 1)),
        ("Daily Generation (kWh):", round(daily_gen_solar)),
        ("Monthly PR%:", round(month_pr_solar, 1)),
        ("Monthly Generation (kWh):", round(month_gen_solar)),
        ("Monthly POA (kWh/m2):", round(monthly_poa_solar / 1000, 2)),
    ]
    row_height, col1_width, col2_width = 48, 320, 280  # increased row height & column widths
    table_width = col1_width + col2_width
    table_left = center_table_horiz(width, table_width)

    for i, (label, value) in enumerate(solar_data):
        row_y = y + i * row_height
        draw.rectangle([table_left, row_y, table_left + col1_width, row_y + row_height], fill=(220,220,220))
        draw.rectangle([table_left + col1_width, row_y, table_left + table_width, row_y + row_height], outline="black", width=2)
        label_x = table_left + (col1_width - draw.textlength(label, font=label_font)) // 2
        value_x = table_left + col1_width + (col2_width - draw.textlength(str(value), font=value_font)) // 2
        draw.text((label_x, row_y + 12), label, fill="black", font=label_font)
        draw.text((value_x, row_y + 12), str(value), fill="black", font=value_font)
    y += len(solar_data) * row_height + 40

    # Wind Data
    draw.text((20, y), f"Wind Generation: {plant_name_wind}", fill="black", font=heading_font)
    y += 40
    wind_data = [
        ("Wind Plant Capacity (MW):", wind_capacity),
        ("Daily Avg Wind Speed (m/s):", round(daily_wind_speed, 2)),
        ("Daily Generation (kWh):", round(daily_gen_wind)),
        ("Monthly Wind Speed (m/s):", round(monthly_wind, 2)),
        ("Monthly Generation (kWh):", round(monthly_gen_wind)),
    ]
    for i, (label, value) in enumerate(wind_data):
        row_y = y + i * row_height
        draw.rectangle([table_left, row_y, table_left + col1_width, row_y + row_height], fill=(220,220,220))
        draw.rectangle([table_left + col1_width, row_y, table_left + table_width, row_y + row_height], outline="black", width=2)
        label_x = table_left + (col1_width - draw.textlength(label, font=label_font)) // 2
        value_x = table_left + col1_width + (col2_width - draw.textlength(str(value), font=value_font)) // 2
        draw.text((label_x, row_y + 12), label, fill="black", font=label_font)
        draw.text((value_x, row_y + 12), str(value), fill="black", font=value_font)
    y += len(wind_data) * row_height + 40

    # Turbine Loc-wise CSV
    if os.path.exists(csv_filename):
        draw.text((20, y), "Wind Turbine Location-Specific Data", fill="black", font=heading_font)
        y += 40
        df = pd.read_csv(csv_filename)
        col_widths = [180, 250, 260]  # increased widths
        table_left = center_table_horiz(width, sum(col_widths))

        # Headers
        colx = table_left
        for i, col in enumerate(df.columns):
            draw.rectangle([colx, y, colx + col_widths[i], y + row_height], fill=(220,220,220))
            draw.rectangle([colx, y, colx + col_widths[i], y + row_height], outline="black", width=2)
            draw.text((colx + 12, y + 12), str(col), fill="black", font=label_font)
            colx += col_widths[i]
        y += row_height

        # Rows
        for _, row in df.iterrows():
            colx = table_left
            for i, val in enumerate(row):
                draw.rectangle([colx, y, colx + col_widths[i], y + row_height], fill="white")
                draw.rectangle([colx, y, colx + col_widths[i], y + row_height], outline="black", width=1)
                draw.text((colx + 12, y + 12), str(val), fill="black", font=table_font)
                colx += col_widths[i]
            y += row_height

    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    image.save(output_file, "JPEG", quality=95)
    return output_file



def generate_dgr_wind_report(plant_name, wind_speed_data, daily_generation, start_date, customer_name, project, ma_percent):

    target_dir = os.path.join("static", "csv_files_turbine")

    # Create the folder if it doesn't exist
    os.makedirs(target_dir, exist_ok=True)
    final_csv_path = os.path.join("static", "csv_files_turbine", f"{plant_name}_DGR_{start_date}.csv")


    try:
        if wind_speed_data.empty or daily_generation.empty:
            # If there's no data, create a single row with zeros
            processed_data = [{
                "Loc No": 0,
                "Avg Wind Speed": 0,
                "Daily Generation (KWh)": 0,
                "MA%": 0
            }]
        else:
            # Merge with daily_generation (left join to retain all wind speed data)
            merged_data = pd.merge(wind_speed_data, daily_generation, on="time", how="left").fillna(0)

            # Extract location numbers
            loc_nos = [col.replace(".Wind-Speed", "") for col in wind_speed_data.columns if col != "time"]

            # Process data
            processed_data = [
                {
                    "Loc No": loc_no,
                    "Avg Wind Speed": round(merged_data[f"{loc_no}.Wind-Speed"].mean(), 2),
                    "Daily Generation (KWh)": round(merged_data.get(f"{loc_no}.Generation today", pd.Series(0)).sum()),
        
                }
                for loc_no in loc_nos
            ]

        # Create DataFrame and save to CSV
        df = pd.DataFrame(processed_data)
        df.to_csv(final_csv_path, index=False)
        return final_csv_path

    except Exception as e:
        logging.error(f"Error generating CSV report: {e}")
        return None



def generate_dgr_wind_pdf(plant_name, start_date, customer_name, project, avg_wind_speed, total_generation, ma_percent,
            monthly_wind, monthly_generation, yearly_wind, yearly_generation):
    output_file = f"Wind_DGR_Summary.pdf"
    try:
        capacity = "10"
        doc = SimpleDocTemplate(output_file, pagesize=A4)
        elements = []
        styles = getSampleStyleSheet()

        # Title without customer, project, and date
        title_name = f"<b><font size=16>{customer_name} - DAILY GENERATION REPORT<br/>Capacity: {capacity} MW | Date: {start_date}</font></b>"

        title = Paragraph(title_name, styles["Title"])
        elements.append(title)
        elements.append(Spacer(1, 12))

        # Cleaned up data table (Removed ma_percent)
        data = [
            [Paragraph("<b>Daily Avg Wind Speed (m/s):</b>", styles["BodyText"]), round(float(avg_wind_speed), 2)],
            [Paragraph("<b>Daily Generation (kWh):</b>", styles["BodyText"]), round(float(total_generation))],
            [Paragraph("<b>Monthly Wind Speed (m/s):</b>", styles["BodyText"]), round(monthly_wind, 2)],
            [Paragraph("<b>Monthly Generation (kWh):</b>", styles["BodyText"]), round(float(monthly_generation))],
        ]

        # Table styling
        table = Table(data, colWidths=[180, 250])
        table.setStyle(TableStyle([
            ('BOX', (0, 0), (-1, -1), 2, colors.black),
            ('GRID', (0, 0), (-1, -1), 1, colors.grey),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('FONTNAME', (0, 0), (-1, -1), "Helvetica"),
            ('FONTSIZE', (0, 0), (-1, -1), 11),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 10),
            ('RIGHTPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))

        elements.append(table)
        doc.build(elements)
        return output_file

    except Exception as e:
        logging.error(f"Error generating Wind DGR PDF: {e}")
        return None

    

def get_monthly_yearly_dates(endDate: str):
    try:
        # Convert string to datetime object
        end_date = datetime.strptime(endDate, '%Y-%m-%d')
        
        # Get the first day of the month
        month_date = end_date.replace(day=1)
        
        # Get the first day of the year
        yearly_date = end_date.replace(month=1, day=1)
        
        return month_date.strftime('%Y-%m-%d'), yearly_date.strftime('%Y-%m-%d')
    except ValueError:
        return "Invalid date format. Please use YYYY-MM-DD."



def csv_to_pdf(csv_filename):
    """
    Converts a CSV file to a professionally styled PDF document.

    Args:
        csv_filename (str): Path to the input CSV file.

    Returns:
        str: Path to the generated PDF file, or None if conversion fails.
    """
    if not csv_filename:
        logging.error("CSV to PDF conversion skipped due to missing CSV file.")
        return None

    # Generate PDF filename by replacing .csv with .pdf
    pdf_filename = csv_filename.replace(".csv", ".pdf")
    
    try:
        # Read the CSV file into a DataFrame
        df = pd.read_csv(csv_filename)

        # Convert DataFrame to a list format for the table
        data = [df.columns.tolist()] + df.values.tolist()

        # Create a PDF document
        doc = SimpleDocTemplate(pdf_filename, pagesize=A4)
        elements = []

        # Define the table with professional styling
        table = Table(data)
        table.setStyle(TableStyle([
            # Header row styling
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#2C3E50')),  # Dark Blue
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.HexColor('#FFFFFF')),   # White
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),

            # Data rows styling
            ('BACKGROUND', (0, 1), (-1, -1), colors.HexColor('#F4F6F7')),  # Light Grey
            ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('ALIGN', (0, 1), (-1, -1), 'CENTER'),
            ('BOTTOMPADDING', (0, 1), (-1, -1), 8),

            # Alternating row colors for better readability
            ('BACKGROUND', (0, 1), (-1, -1), colors.HexColor('#ECF0F1')),  # Light Grey for alternate rows
            ('BACKGROUND', (0, 2), (-1, -1), colors.HexColor('#FFFFFF')),  # White for other rows

            # Gridlines for the table
            ('GRID', (0, 0), (-1, -1), 0.5, colors.HexColor('#BDC3C7'))   # Soft Grey Grid
        ]))

        # Add the table to the PDF elements
        elements.append(table)
        doc.build(elements)
        
        logging.info(f"CSV converted to PDF successfully: {pdf_filename}")
        return pdf_filename

    except Exception as e:
        logging.error(f"Error converting CSV to PDF: {e}", exc_info=True)
        return None


def merge_pdfs(final_plot_path, pdf1, output_filename):
    if not pdf1 or not final_plot_path:
        logging.error("Skipping PDF merging due to missing files.")
        return None

    try:
        merger = PdfMerger()
        merger.append(final_plot_path)
        merger.append(pdf1)
        # merger.append(pdf2)
        merger.write(output_filename)
        merger.close()
        return output_filename
    except Exception as e:
        logging.error(f"Error merging PDFs: {e}")
        return None


def merge_pdfs_wind(pdf1, pdf2, output_filename):
    if not pdf1 or not pdf2:
        logging.error("Skipping PDF merging due to missing files.")
        return None

    try:
        merger = PdfMerger()
        merger.append(pdf1)
        merger.append(pdf2)
        merger.write(output_filename)
        merger.close()
        return output_filename
    except Exception as e:
        logging.error(f"Error merging PDFs: {e}")
        return None



def fetch_data_total(plant_name, params, category, start_date, end_date, condition, retries=3):
    """
    Fetches data from Prescinto API with error handling and retries.
    Returns DataFrame with columns even if there are no rows.
    """
    for attempt in range(retries):
        try:
            data = PRESCINTO_API.fetchDataV2(plant_name, category, params, None, start_date, end_date, granularity='1d', condition=condition)
            if isinstance(data, str):  # API returned an error message as a string
                logging.error(f"Error fetching fetch_data_total for plant - {plant_name} - {params} (Attempt {attempt+1}): {data}")
                continue
            df = pd.DataFrame(data)
            # Return DataFrame if it has columns, even if it has no rows
            if not df.empty or (df.shape[0] == 0 and df.shape[1] > 0):
                return df
        except Exception as e:
            logging.error(f"Exception fetching fetch_data_total for plant - {plant_name} -  {params} (Attempt {attempt+1}): {e}")
        time.sleep(5)  # Wait before retrying
    return pd.DataFrame()



def get_dynamic_dates(end_date_str):
    """
    Given an end date (YYYY-MM-DD), this function calculates:
    - Start and end dates of last month
    - Start and end dates of last year
    - Start date of the current month
    - Start date of the last 30 days (including 1 extra day)
    - Start date of the current year

    :param end_date_str: String format of the end date (e.g., '2025-03-06')
    :return: Tuple (current_month_start, last_month_start, last_month_end, last_year_start, last_year_end, last_30_days_start, current_year_start)
    """
    end_date = datetime.strptime(end_date_str, "%Y-%m-%d")  # Convert string to date
    
    # Start date of the given month
    current_month_start = end_date.replace(day=1)
    
    # Last month start and end dates
    last_month_start = (end_date.replace(day=1) - relativedelta(months=1))
    last_month_end = (end_date.replace(day=1) - timedelta(days=1))

    # Last year start and end dates
    last_year_start = end_date.replace(year=end_date.year - 1)
    last_year_end = last_year_start.replace(month=12, day=31)
    
    # Start date for last 30 days (including one extra day)
    last_30_days_start = end_date - timedelta(days=30)

    # **Start date of the current year**
    current_year_start = end_date.replace(month=1, day=1)

    last_year_date = end_date - relativedelta(years=1)

    return (current_month_start.strftime("%Y-%m-%d"),
            last_month_start.strftime("%Y-%m-%d"),
            last_month_end.strftime("%Y-%m-%d"),
            last_year_start.strftime("%Y-%m-%d"),
            last_year_end.strftime("%Y-%m-%d"),
            last_30_days_start.strftime("%Y-%m-%d"),
            current_year_start.strftime("%Y-%m-%d"),
            last_year_date.strftime("%Y-%m-%d"))

def get_capacity_from_csv(plant_id):
    csv_path = Config.CUSTOMER_DATA_CSV_PATH
    return next((row['Capacity ( MW)'].strip() for row in csv.DictReader(open(csv_path, encoding='utf-8')) if row['Plant id'].strip() == plant_id), "N/A")


def get_turbine_metadata_from_csv(plant_id):
    csv_path = Config.CUSTOMER_DATA_CSV_PATH
    return next((row['Turbine Metadata'].strip() for row in csv.DictReader(open(csv_path, encoding='utf-8')) if row['Plant id'].strip() == plant_id), "N/A")


def get_inverter_metadata_from_csv(plant_id):
    csv_path = Config.CUSTOMER_DATA_CSV_PATH
    return next((row['Inverter Metadata'].strip() for row in csv.DictReader(open(csv_path, encoding='utf-8')) if row['Plant id'].strip() == plant_id), "N/A")




def get_both_plant_pairs_from_csv(csv_file_path):
    """
    Parses the CSV file and returns a list of (solar_plant_id, wind_plant_id, customer_name)
    tuples for entries that have the same non-empty 'Combined' field (like 'both1', 'both2').
    """
    combined_map = defaultdict(dict)  # key = combined tag, value = {'solar': ..., 'wind': ..., 'customer': ...}
    both_plant_pairs = []

    try:
        with open(csv_file_path, mode='r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                combined = row.get('Combined', '').strip().lower()
                plant_type = row.get('Type', '').strip().lower()
                plant_id = row.get('Plant id', '').strip()
                customer_name = row.get('Customer Name', '').strip()

                if combined and combined.startswith('both') and plant_id and customer_name:
                    if plant_type in ['solar', 'wind']:
                        combined_map[combined][plant_type] = plant_id
                        combined_map[combined]['customer'] = customer_name

        for tag, data in combined_map.items():
            if 'solar' in data and 'wind' in data:
                both_plant_pairs.append((data['solar'], data['wind'], data['customer']))

    except Exception as e:
        logging.error(f"Error reading both plant data from CSV: {e}")

    return both_plant_pairs





def get_data_wind_solar(csv_file_path, plant_type):
    """
    Reads wind plant and customer pairs from the given CSV file.
    Filters:
      - Only rows with Type == 'Wind'
      - Excludes rows where Combined column has any non-empty value
    Returns a list of (plant_id, customer_name) tuples.
    """
    plant_customer_pairs = []

    try:
        with open(csv_file_path, mode='r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                if row['Type'].strip().lower() == plant_type:
                    combined = row.get('Combined', '').strip().lower()
                    if combined:  # Skip if there's any value in Combined
                        continue
                    plant_id = row['Plant id'].strip()
                    customer_name = row['Customer Name'].strip()
                    if plant_id and customer_name:
                        plant_customer_pairs.append((plant_id, customer_name))
    except Exception as e:
        logging.error(f"Error reading CSV file '{csv_file_path}': {e}")

    return plant_customer_pairs



def merge_pdfs_both_plants(summary_pdf, gen_plot_year, wind_plot_year, combined_30, final_plot_path, final_pdf_path):
    
    if not summary_pdf or not gen_plot_year:
        logging.error("Skipping PDF merging due to missing files.")
        return None

    try:
        merger = PdfMerger()
        merger.append(summary_pdf)
        merger.append(gen_plot_year)
        merger.append(wind_plot_year)
        merger.append(combined_30)
        merger.append(final_plot_path)
        merger.write(final_pdf_path)
        merger.close()
        return final_pdf_path
    except Exception as e:
        logging.error(f"Error merging PDFs: {e}")
        return None
    

def get_contact_number_from_csv(plant_id):
    csv_path = Config.CUSTOMER_DATA_CSV_PATH
    # Find the contact numbers, strip any extra spaces, and split by commas
    test_numbers = next(
        (row['Test Number'].strip().split(',') for row in csv.DictReader(open(csv_path, encoding='utf-8')) if row['Plant id'].strip() == plant_id),
        ["N/A"]
    )
    # contact_numbers = next(
    #     (row['Contact Number'].strip().split(',') for row in csv.DictReader(open(csv_path, encoding='utf-8')) if row['Plant id'].strip() == plant_id),
    #     ["N/A"]
    # )
    # Strip spaces from each number in the list
    return [number.strip() for number in test_numbers]# + contact_numbers]






def normalize_number(number):
    """Remove all non-digit characters and trim leading '91' if present."""
    num = re.sub(r'\D', '', number.strip())
    if num.startswith('91') and len(num) > 10:
        num = num[2:]
    return num


def get_metadata_by_contact_number(input_number, plant_id):
    """
    Given a contact number and plant_id, return the full plant metadata row
    from the CSV where both match: plant_id matches 'Plant id' and input_number matches
    Contact Number or Test Number in that row.
    If found in Contact Number, maps the number to the corresponding contact person.
    """
    input_number = normalize_number(input_number)
    plant_id = str(plant_id).strip()
    csv_path = Config.CUSTOMER_DATA_CSV_PATH

    with open(csv_path, encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            row_plant_id = row.get('Plant id', '').strip()
            if row_plant_id != plant_id:
                continue

            contact_numbers_raw = row.get('Contact Number', '')
            test_numbers_raw = row.get('Test Number', '')
            contact_persons_raw = row.get('Contact Person', '')

            # Normalize contact/test numbers
            contact_numbers_list = [normalize_number(num) for num in contact_numbers_raw.split(',') if normalize_number(num)]
            test_numbers_list = [normalize_number(num) for num in test_numbers_raw.split(',') if normalize_number(num)]
            contact_persons_list = [name.strip() for name in contact_persons_raw.split(',')]

            all_numbers = contact_numbers_list + test_numbers_list

            if input_number in all_numbers:
                if input_number in contact_numbers_list:
                    idx = contact_numbers_list.index(input_number)
                    matched_person = contact_persons_list[idx] if idx < len(contact_persons_list) else (contact_persons_list[-1] if contact_persons_list else "")
                else:
                    matched_person = "Test Number Contact"

                return {
                    "plant_id": row_plant_id,
                    "client_name": row.get('Customer Name', '').strip(),
                    "type": row.get('Type', '').strip(),
                    "capacity": row.get('Capacity ( MW)', '').strip(),
                    "combined": row.get('Combined', '').strip(),
                    "contact_person": matched_person,
                    "contact_number": contact_numbers_raw.strip(),
                    "test_number": test_numbers_raw.strip()
                }

    return {}  # No match found



def _safe_mean(df: pd.DataFrame) -> float:
    return df.iloc[:, 1:].mean().mean() if not df.empty else 0.0


def _safe_sum(df: pd.DataFrame) -> float:
    return df.iloc[:, 1:].sum().sum() if not df.empty else 0.0




def get_hybrid_info_for_plant_id(plant_id, csv_file_path):
    """
    Given a plant_id, returns a tuple (is_hybrid, hybrid_tag, other_plant_id, plant_type, customer_name).
    - is_hybrid: True if plant_id is part of a hybrid (both) plant, else False.
    - hybrid_tag: the value of the 'Combined' field (e.g., 'both1'), or '' if not hybrid.
    - other_plant_id: the paired plant_id (wind if input is solar, solar if input is wind), or '' if not hybrid.
    - plant_type: 'solar' or 'wind'
    - customer_name: the customer name for this plant
    """
    import csv
    hybrid_map = {}
    try:
        with open(csv_file_path, mode='r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                combined = row.get('Combined', '').strip().lower()
                plant_type = row.get('Type', '').strip().lower()
                pid = row.get('Plant id', '').strip()
                customer_name = row.get('Customer Name', '').strip()
                if not pid:
                    continue
                if combined and combined.startswith('both'):
                    if combined not in hybrid_map:
                        hybrid_map[combined] = {}
                    hybrid_map[combined][plant_type] = (pid, customer_name)
    except Exception as e:
        # Optionally log error
        return (False, '', '', '', '')

    for tag, pair in hybrid_map.items():
        for typ in ['solar', 'wind']:
            if typ in pair and pair[typ][0] == plant_id:
                other_typ = 'wind' if typ == 'solar' else 'solar'
                other_plant_id = pair[other_typ][0] if other_typ in pair else ''
                customer_name = pair[typ][1]
                return (True, tag, other_plant_id, typ, customer_name)
    return (False, '', '', '', '')
