from DB.models import SolarReport, WindReport, DgrBothDb, WhatsAppMessage, ChatHistory, WindTurbineData, SolarInverterData
from DB.setup_db import session
from helper.logger_setup import setup_logger
from sqlalchemy.exc import SQLAlchemyError


# Initialize logger
logger = setup_logger("db_ops", "db_ops.log")

def insert_wind_data_db(records):
    """
    Inserts or updates wind DGR records into the database and returns their generated IDs.

    Args:
        records (List[Dict]): List of dictionaries containing wind DGR data.

    Returns:
        List[int]: List of IDs of inserted/updated records.
    """
    inserted_ids = []
    try:
        logger.info(f"Inserting/updating {len(records)} wind records into DB.")
        for record in records:
            existing = session.query(WindReport).filter_by(
                plant_short_name=record.get("plant_short_name"),
                date=record.get("date")
            ).first()
            if existing:
                for key, value in record.items():
                    setattr(existing, key, value)
                logger.info(f"Updated wind record for {record.get('plant_short_name')} on {record.get('date')}")
                inserted_ids.append(existing.id)
            else:
                obj = WindReport(**record)
                session.add(obj)
                session.flush()  # To get the id before commit
                inserted_ids.append(obj.id)
                logger.info(f"Inserted new wind record for {record.get('plant_short_name')} on {record.get('date')}")
        session.commit()
        logger.info("Wind data inserted/updated successfully.")
        return inserted_ids
    except Exception as e:
        session.rollback()
        logger.exception("Error inserting/updating wind data:")
        return []
    finally:
        session.close()
        logger.debug("DB session closed for wind data.")


def insert_wind_turbine_data_db(records):
    """
    Inserts or updates wind turbine-wise records into the database and returns their generated IDs.

    Args:
        records (List[Dict]): List of dictionaries containing wind turbine data.

    Returns:
        List[int]: List of IDs of inserted/updated records.
    """
    inserted_ids = []
    try:
        logger.info(f"Inserting/updating {len(records)} wind turbine records into DB.")
        for record in records:
            existing = session.query(WindTurbineData).filter_by(
                plant_id=record.get("plant_id"),
                turbine_name=record.get("turbine_name"),
                date=record.get("date")
            ).first()
            if existing:
                for key, value in record.items():
                    setattr(existing, key, value)
                logger.info(f"Updated wind turbine record for {record.get('plant_id')} - {record.get('turbine_name')} on {record.get('date')}")
                inserted_ids.append(existing.id)
            else:
                obj = WindTurbineData(**record)
                session.add(obj)
                session.flush()  # To get the id before commit
                inserted_ids.append(obj.id)
                logger.info(f"Inserted new wind turbine record for {record.get('plant_id')} - {record.get('turbine_name')} on {record.get('date')}")
        session.commit()
        logger.info("Wind turbine data inserted/updated successfully.")
        return inserted_ids
    except Exception as e:
        session.rollback()
        logger.exception("Error inserting/updating wind turbine data:")
        return []
    finally:
        session.close()
        logger.debug("DB session closed for wind turbine data.")


import math
import numpy as np

def sanitize_record_for_db(record):
    """
    Converts any NaN or infinite float values in the record to None for DB compatibility.
    Handles both Python and numpy float types.
    """
    sanitized = {}
    for k, v in record.items():
        if isinstance(v, float) and (math.isnan(v) or math.isinf(v)):
            sanitized[k] = None
        elif isinstance(v, np.floating) and (np.isnan(v) or np.isinf(v)):
            sanitized[k] = None
        else:
            sanitized[k] = v
    return sanitized

def insert_solar_inverter_data_db(records):
    """
    Inserts or updates solar inverter-wise records into the database and returns their generated IDs.

    Args:
        records (List[Dict]): List of dictionaries containing solar inverter data.

    Returns:
        List[int]: List of IDs of inserted/updated records.
    """
    inserted_ids = []
    try:
        logger.info(f"Inserting/updating {len(records)} solar inverter records into DB.")
        for record in records:
            record = sanitize_record_for_db(record)
            existing = session.query(SolarInverterData).filter_by(
                plant_id=record.get("plant_id"),
                inverter_name=record.get("inverter_name"),
                date=record.get("date")
            ).first()
            if existing:
                for key, value in record.items():
                    setattr(existing, key, value)
                logger.info(f"Updated solar inverter record for {record.get('plant_id')} - {record.get('inverter_name')} on {record.get('date')}")
                inserted_ids.append(existing.id)
            else:
                obj = SolarInverterData(**record)
                session.add(obj)
                session.flush()  # To get the id before commit
                inserted_ids.append(obj.id)
                logger.info(f"Inserted new solar inverter record for {record.get('plant_id')} - {record.get('inverter_name')} on {record.get('date')}")
        session.commit()
        logger.info("Solar inverter data inserted/updated successfully.")
        return inserted_ids
    except Exception as e:
        session.rollback()
        logger.exception("Error inserting/updating solar inverter data:")
        return []
    finally:
        session.close()
        logger.debug("DB session closed for solar inverter data.")

def insert_solar_data_db(records):
    """
    Inserts or updates solar DGR records into the database.

    Args:
        records (List[Dict]): List of dictionaries containing solar DGR data.
    """
    try:
        logger.info(f"Inserting/updating {len(records)} solar records into DB.")
        for record in records:
            existing = session.query(SolarReport).filter_by(
                plant_short_name=record.get("plant_short_name"),
                date=record.get("date")
            ).first()
            if existing:
                for key, value in record.items():
                    setattr(existing, key, value)
                logger.info(f"Updated solar record for {record.get('plant_short_name')} on {record.get('date')}")
            else:
                obj = SolarReport(**record)
                session.add(obj)
                logger.info(f"Inserted new solar record for {record.get('plant_short_name')} on {record.get('date')}")
        session.commit()
        logger.info("Solar data inserted/updated successfully.")
    except Exception as e:
        session.rollback()
        logger.exception("Error inserting/updating solar data:")
        return []
    finally:
        session.close()
        logger.debug("DB session closed for solar data.")

def insert_both_data_db(records):
    """
    Inserts or updates combined DGR records into the database.

    Args:
        records (List[Dict]): List of dictionaries containing combined DGR data.
    """
    inserted_ids = []
    try:
        logger.info(f"Inserting/updating {len(records)} combined records into DB.")
        for record in records:
            existing = session.query(DgrBothDb).filter_by(
                plant_short_name_solar=record.get("plant_short_name_solar"),
                plant_short_name_wind=record.get("plant_short_name_wind"),
                date=record.get("date")
            ).first()
            if existing:
                for key, value in record.items():
                    setattr(existing, key, value)
                logger.info(f"Updated combined record for {record.get('plant_short_name_solar')}, {record.get('plant_short_name_wind')} on {record.get('date')}")
                inserted_ids.append(existing.id)
            else:
                obj = DgrBothDb(**record)
                session.add(obj)
                session.flush()
                inserted_ids.append(obj.id)
                logger.info(f"Inserted new combined record for {record.get('plant_short_name_solar')}, {record.get('plant_short_name_wind')} on {record.get('date')}")
        session.commit()
        logger.info("Combined wind + solar data inserted/updated successfully.")
        return inserted_ids
    except Exception as e:
        session.rollback()
        logger.exception("Error inserting/updating combined data:")
        return []
    finally:
        session.close()
        logger.debug("DB session closed for combined data.")

def insert_message_data_db(records: list[dict]):
    """
    Inserts WhatsApp message records into the database.

    Args:
        records (List[Dict]): List of dictionaries containing WhatsApp message data.
    """
    
    try:
        logger.info(f"Inserting {len(records)} WhatsApp message record(s) into DB.")
        objs = [WhatsAppMessage(**record) for record in records]
        session.add_all(objs)
        session.commit()
        logger.info("WhatsApp message data inserted successfully.")
    except Exception as e:
        session.rollback()
        logger.exception("Error inserting WhatsApp message data:")
    finally:
        session.close()
        logger.debug("DB session closed for WhatsApp message insert.")


def fetch_messages_by_message_id(message_id: str) -> list[dict]:
    """
    Fetches all WhatsApp message records that match a given message_id.

    Args:
        message_id (str): The WhatsApp message ID to filter by.

    Returns:
        List[dict]: List of message records as dictionaries.
    """
    try:
        logger.info(f"Fetching records for message_id: {message_id}")
        results = session.query(WhatsAppMessage).filter(WhatsAppMessage.message_id == message_id).all()
        logger.info(f"Found {len(results)} record(s) for message_id {message_id}")
        return [msg.to_dict() for msg in results]
    except Exception as e:
        logger.exception("Error fetching WhatsApp message records:")
        return []
    finally:
        session.close()
        logger.debug("DB session closed after fetching message records.")


def insert_chat_history(records: list[dict]):
    """
    Inserts chat history records into the database.

    Args:
        records (List[Dict]): List of dictionaries containing chat history data.
            Each dict should have: thread_id, role, content, timestamp
    """
    try:
        logger.info(f"Inserting {len(records)} chat history record(s) into DB.")
        objs = [ChatHistory(**record) for record in records]
        session.add_all(objs)
        session.commit()
        logger.info("Chat history data inserted successfully.")
    except Exception as e:
        session.rollback()
        logger.exception("Error inserting chat history data:")
    finally:
        session.close()
        logger.debug("DB session closed for chat history insert.")

def fetch_chat_history(thread_id: str, last_n: int = 7) -> list[dict]:
    """
    Fetches the last N chat history records for a given thread_id, ordered by id ascending (chronological).

    Args:
        thread_id (str): The thread ID to filter by.
        last_n (int): Number of most recent records to fetch (default 7).

    Returns:
        List[dict]: List of chat history records as dictionaries.
    """
    try:
        logger.info(f"Fetching last {last_n} chat history records for thread_id: {thread_id}")
        results = (
            session.query(ChatHistory)
            .filter(ChatHistory.thread_id == thread_id)
            .order_by(ChatHistory.id.desc())
            .limit(last_n)
            .all()
        )
        # Reverse to chronological order
        results = list(reversed(results))
        logger.info(f"Found {len(results)} chat history record(s) for thread_id {thread_id}")
        return [msg.to_dict() for msg in results]
    except Exception as e:
        logger.exception("Error fetching chat history records:")
        return []
    finally:
        session.close()
        logger.debug("DB session closed after fetching chat history records.")

def delete_chat_history(thread_id: str):
    """
    Deletes all chat history records for a given thread_id.

    Args:
        thread_id (str): The thread ID to delete history for.
    """
    try:
        logger.info(f"Deleting chat history for thread_id: {thread_id}")
        session.query(ChatHistory).filter(ChatHistory.thread_id == thread_id).delete()
        session.commit()
        logger.info("Chat history deleted successfully.")
    except Exception as e:
        session.rollback()
        logger.exception("Error deleting chat history:")
    finally:
        session.close()
        logger.debug("DB session closed after deleting chat history.")


# def get_solar_report_data(plant_short_name, report_date):
#     """
#     Fetches POA, DGR path, PR, and Generation for a given plant and date from SolarReport.

#     Args:
#         plant_short_name (str): The short name of the plant.
#         report_date (datetime.date): The report date.

#     Returns:
#         dict: A dictionary with 'poa', 'dgr_path', 'pr', and 'generation' if found, else None.
#     """
#     try:
#         record = session.query(SolarReport).filter_by(
#             plant_short_name=plant_short_name,
#             date=report_date
#         ).first()

#         if record:
#             return {
#                 "poa": record.poa,
#                 "dgr_path": getattr(record, 'dgr_path', None),
#                 "pr": record.pr,
#                 "generation": record.generation
#             }
#         else:
#             print("⚠️ No matching solar report found.")
#             return None

#     except SQLAlchemyError as e:
#         print("❌ Error fetching solar report data:", e)
#         return None

#     finally:
#         session.close()



def get_solar_report_data(plant_short_name, report_date):
    """
    Fetches complete solar report data for a given plant and date from SolarReport.
    Returns all relevant fields, including edit values and status flags.
    """
    try:
        record = session.query(SolarReport).filter_by(
            plant_short_name=plant_short_name,
            date=report_date
        ).first()

        if record:
            return record.to_dict()
        else:
            print("⚠️ No matching solar report found.")
            return None

    except SQLAlchemyError as e:
        print("❌ Error fetching solar report data:", e)
        return None

    finally:
        session.close()





def get_dgr_path_by_id_solar(report_id):
    """
    Fetches the DGR path for a given SolarReport based on the report ID.

    Args:
        report_id (int): The ID of the solar report.

    Returns:
        str: The DGR path if found, else None.
    """
    try:
        record = session.query(SolarReport).filter_by(id=report_id).first()

        if record:
            return record.dgr_path
        else:
            print("⚠️ No matching solar report found with ID:", report_id)
            return None

    except SQLAlchemyError as e:
        print("❌ Error fetching DGR path:", e)
        return None

    finally:
        session.close()


# def get_wind_report_data(plant_short_name, report_date):
#     """
#     Fetches wind_speed, DGR path and Generation for a given plant and date from WindReport.

#     Args:
#         plant_short_name (str): The short name of the plant.
#         report_date (datetime.date): The report date.

#     Returns:
#         dict: A dictionary with 'wind_speed', 'dgr_path', and 'generation' if found, else None.
#     """
#     try:
#         record = session.query(WindReport).filter_by(
#             plant_short_name=plant_short_name,
#             date=report_date
#         ).first()

#         if record:
#             return {
#                 "wind_speed": record.wind_speed,
#                 "dgr_path": getattr(record, 'dgr_path', None),
#                 "generation": record.generation
#             }
#         else:
#             print("⚠️ No matching solar report found.")
#             return None

#     except SQLAlchemyError as e:
#         print("❌ Error fetching solar report data:", e)
#         return None

#     finally:
#         session.close()



def get_wind_report_data(plant_short_name, report_date):
    """
    Fetches complete wind report data for a given plant and date from WindReport.
    Returns all relevant fields, including edit values and status flags.
    """
    try:
        record = session.query(WindReport).filter_by(
            plant_short_name=plant_short_name,
            date=report_date
        ).first()

        if record:
            return record.to_dict()
        else:
            print("⚠️ No matching wind report found.")
            return None

    except SQLAlchemyError as e:
        print("❌ Error fetching wind report data:", e)
        return None

    finally:
        session.close()




# def get_combine_report_data(plant_short_name_wind, report_date):
#     """
#     Fetches wind_speed, DGR path and Generation for a given plant and date from WindReport.

#     Args:
#         plant_short_name (str): The short name of the plant.
#         report_date (datetime.date): The report date.

#     Returns:
#         dict: A dictionary with 'wind_speed', 'dgr_path', and 'generation' if found, else None.
#     """
#     try:
#         record = session.query(DgrBothDb).filter_by(
#             plant_short_name_wind=plant_short_name_wind,
#             date=report_date
#         ).first()

#         if record:
#             return {
#             "wind_speed": record.wind_speed,
#             "dgr_path": getattr(record, 'dgr_path', None),
#             'generation_solar': record.generation_solar,
#             'pr': record.pr,
#             'poa': record.poa,
#             'generation_wind': record.generation_wind
#             }
#         else:
#             print("⚠️ No matching solar report found.")
#             return None

#     except SQLAlchemyError as e:
#         print("❌ Error fetching solar report data:", e)
#         return None

#     finally:
#         session.close()


def get_combine_report_data(plant_short_name_wind, report_date):
    """
    Fetches combined wind + solar report data for a given plant and date from DgrBothDb.
    Returns all relevant fields, including edit values and status flags.
    """
    try:
        record = session.query(DgrBothDb).filter_by(
            plant_short_name_wind=plant_short_name_wind,
            date=report_date
        ).first()

        if record:
            return record.to_dict()
        else:
            print("⚠️ No matching combined report found.")
            return None

    except SQLAlchemyError as e:
        print("❌ Error fetching combined report data:", e)
        return None

    finally:
        session.close()



def get_combine_report_data_solar(plant_short_name_solar, report_date):
    """
    Fetches combined wind + solar report data for a given plant and date from DgrBothDb.
    Returns all relevant fields, including edit values and status flags.
    """
    try:
        record = session.query(DgrBothDb).filter_by(
            plant_short_name_solar=plant_short_name_solar,
            date=report_date
        ).first()

        if record:
            return record.to_dict()
        else:
            print("⚠️ No matching combined report found.")
            return None

    except SQLAlchemyError as e:
        print("❌ Error fetching combined report data:", e)
        return None

    finally:
        session.close()
