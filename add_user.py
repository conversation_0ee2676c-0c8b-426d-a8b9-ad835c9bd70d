import sys
from getpass import getpass
from DB.setup_db import session as db_session
from DB.models import User
from werkzeug.security import generate_password_hash

def main():
    print("Add a new user to the users table")
    username = input("Username: ").strip()
    email = input("Email: ").strip()
    password = input("Password: ").strip()
    if not username or not email or not password:
        print("All fields are required.")
        sys.exit(1)
    db = db_session()
    try:
        if db.query(User).filter((User.username == username) | (User.email == email)).first():
            print("A user with this username or email already exists.")
            sys.exit(1)
        password_hash = generate_password_hash(password)
        user = User(username=username, email=email, password_hash=password_hash)
        db.add(user)
        db.commit()
        print(f"User '{username}' added successfully.")
    except Exception as e:
        db.rollback()
        print("Error adding user:", e)
    finally:
        db_session.remove()

if __name__ == "__main__":
    main()
