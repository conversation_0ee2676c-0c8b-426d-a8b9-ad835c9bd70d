# Wind & Solar Automation Platform

A modular, production-ready platform for automating daily generation reports (DGR) for wind and solar plants. The platform integrates with WhatsApp for report delivery, supports advanced data processing, plotting, PDF/CSV generation, and is designed for extensibility and robust operations.

---

## Table of Contents

- [Features](#features)
- [Project Structure](#project-structure)
- [Setup & Installation](#setup--installation)
- [Configuration](#configuration)
- [Usage](#usage)
- [Directory Overview](#directory-overview)
- [Exports](#exports)
- [MCP Bot Integration](#mcp-bot-integration)
- [API Endpoints](#api-endpoints)
- [Deployment](#deployment)
- [Troubleshooting & FAQ](#troubleshooting--faq)
- [Contributing](#contributing)
- [License](#license)

---

## Features

- Automated generation of daily, monthly, and yearly reports for wind and solar plants.
- WhatsApp integration for sending DGR reports to customers.
- Modular codebase with clear separation of business logic, data access, utilities, and configuration.
- Asynchronous processing for efficient data handling.
- PDF and CSV report generation with professional formatting.
- Centralized configuration and environment management.
- Logging and error handling for robust operations.
- Extensible architecture for new integrations (e.g., MCP Bot).
- Support for S3 storage and cloud-based workflows.
- Web frontend for report access and management.
- Custom plotting utilities for wind and solar data.
- Memory-augmented automation via MCP Bot.
- Multi-customer support and flexible scheduling.

---

## Project Structure

```
.
├── app/                # Main application logic (routes, handlers, scheduling, webhooks)
├── DB/                 # Database models and operations (SQLAlchemy)
├── helper/             # Utilities: plotting, integrations, logging, S3, etc.
├── src/                # Core automation logic for wind/solar/both
├── whatsapp/           # WhatsApp integration (sending, extraction)
├── config/             # Configuration and settings
├── static/             # Static files (customer data, logos, reports, plots)
├── templates/          # HTML templates for web frontend
├── mcp_bot/            # MCP Bot integration (client/server for automation)
├── exports/            # Exported reports and data
├── requirements.txt    # Python dependencies
├── .env                # Environment variables (not committed)
├── README.md           # Project documentation
├── main.py             # Application entry point
```

---

## Setup & Installation

1. **Clone the repository:**
   ```bash
   git clone https://github.com/Logos-Labs-India/DGR-Generation.git
   cd DGR-Generation
   ```

2. **Create and activate a virtual environment:**
   ```bash
   python -m venv venv
   # On Unix/macOS:
   source venv/bin/activate
   # On Windows:
   venv\Scripts\activate
   ```

3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables:**
   - Copy `.env.example` to `.env` and fill in the required values (see [Configuration](#configuration)).

---

## Configuration

All configuration is managed via environment variables and `config/settings.py`. Key settings include:

- `VERIFY_TOKEN` - Token for webhook verification.
- `PUBLIC_URL` - Publicly accessible URL for webhooks.
- `DEBUG` - Set to `True` for development.
- `CUSTOMER_DATA_CSV_PATH` - Path to the customer data CSV file.
- `API_TOKEN` - Token for external integrations (e.g., Prescinto API).
- `S3_BUCKET` - (Optional) S3 bucket for cloud storage.
- `WHATSAPP_API_KEY` - (Optional) API key for WhatsApp integration.

**Example `.env` file:**
```
VERIFY_TOKEN=your_verify_token
PUBLIC_URL=https://yourdomain.com
DEBUG=True
CUSTOMER_DATA_CSV_PATH=static/customer_data - Sheet1.csv
API_TOKEN=your_api_token
S3_BUCKET=your_s3_bucket
WHATSAPP_API_KEY=your_whatsapp_api_key
```

---

## Usage

**Start the application:**

- **On Windows:**  
  Double-click `run_project.bat` or run it from the command prompt:
  ```bat
  run_project.bat
  ```
  This batch file sets up the environment and starts the application automatically.

- **On other platforms:**  
  Run the following command:
  ```bash
  python main.py
  ```

- The app will start the web server and background scheduler.
- Reports will be generated and sent via WhatsApp as per the schedule.

**Access the web frontend:**
- Visit `http://localhost:5000` (or your configured host/port).

**Regenerate reports manually:**
```bash
python app/regenerate_reports.py
```

---

## Directory Overview

- **app/**: Web routes, frontend handlers, scheduling, and webhook endpoints.
- **DB/**: SQLAlchemy models and database operations.
- **helper/**: Utilities for plotting, PDF/CSV generation, logging, S3, and integrations.
- **src/**: Core automation for wind, solar, and combined plants.
- **whatsapp/**: WhatsApp message sending and extraction logic.
- **config/**: Centralized configuration and environment variable loading.
- **static/**: Customer data, logos, generated reports, and plots.
- **templates/**: HTML templates for the web interface.
- **mcp_bot/**: MCP Bot client/server for automation and integration with external tools.
- **exports/**: Exported reports and data for external use.

---

## Exports

The `exports/` directory contains generated reports and data files that can be used for further analysis or sharing with stakeholders.

---

## MCP Bot Integration

The `mcp_bot/` directory provides integration with the Model Context Protocol (MCP) for advanced automation, external tool integration, and memory management. See `mcp_bot/README.md` (if available) for details.

---

## API Endpoints

The application exposes several HTTP endpoints for automation and integration. Key endpoints include:

- `POST /webhook` — Receives WhatsApp and external webhooks.
- `GET /` — Web frontend dashboard.
- `POST /regenerate` — Trigger report regeneration (see `app/regenerate_reports.py`).
- Additional endpoints for report download, status, and customer management may be available.

See `app/routes.py` and `app/webhook.py` for details.

---

## Deployment

- Use environment variables to control deployment settings.
- For production, consider using a WSGI server (e.g., Gunicorn) and a process manager (e.g., Supervisor, systemd).
- Containerization (Docker) is recommended for scalable deployments.
- Ensure secure management of secrets and environment variables.

---

## Troubleshooting & FAQ

**Q: WhatsApp messages are not being sent.**
- Check your WhatsApp API credentials and network connectivity.
- Review logs in `logs/send_whatsapp.log`.

**Q: Reports are not generated or are empty.**
- Ensure customer data CSV is present and correctly formatted.
- Check for errors in the logs and verify data sources.

**Q: How do I add a new customer or plant?**
- Update the customer data CSV in `static/`.
- Restart the application if necessary.

**Q: How do I customize report templates?**
- Edit HTML templates in the `templates/` directory.

---

## Contributing

Contributions are welcome! Please open issues or pull requests for bug fixes, improvements, or new features.

---

## License

This project is licensed under the MIT License. See [LICENSE](LICENSE) for details.
