import asyncio
from mcp_bot.client_runner import run_agent

local_number = "8866383755"

async def main():
    print("Chatbot is running. Type your message below.")
    print("Press Ctrl+C to exit.\n")
    while True:
        try:
            incoming_message = input("You: ")
            if not incoming_message.strip():
                continue  # skip empty messages

            response = await run_agent(local_number, incoming_message)
            print("##########################################")
            print("Bot:", response)
            print("##########################################")

        except KeyboardInterrupt:
            print("\nStopping chatbot...")
            break

if __name__ == "__main__":
    asyncio.run(main())

