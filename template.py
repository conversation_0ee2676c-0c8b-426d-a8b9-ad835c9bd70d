import os
from pathlib import Path
import logging

# Logging configuration
logging.basicConfig(level=logging.INFO, format='[%(asctime)s]: %(message)s:')

# List of files and directories to create for this project
list_of_files = [
    ".env",
    ".gitignore",
    ".gitkeep",
    "CHANGELOG.md",
    "main.py",
    "README.md",
    "requirements.txt",
    "setup.py",
    "app/__init__.py",
    "app/frontend_handler.py",
    "app/process_tasks.py",
    "app/process_whatsapp.py",
    "app/regenerate_reports.py",
    "app/routes.py",
    "app/schedule_task.py",
    "app/webhook.py",
    "config/__init__.py",
    "config/creds_storage_s3.py",
    "config/settings.py",
    "DB/__init__.py",
    "DB/db_ops.py",
    "DB/mcp_db_setup.py",
    "DB/models.py",
    "DB/schema_definition.md",
    "DB/setup_db.py",
    "export_plots/.gitkeep",
    "exports/.gitkeep",
    "helper/__init__.py",
    "helper/integration_utilities.py",
    "helper/logger_setup.py",
    "helper/solar_plots_enhanced.py",
    "helper/solar_plots.py",
    "helper/storage_s3.py",
    "helper/utils.py",
    "helper/wind_plots_enhanced.py",
    "helper/wind_plots.py",
    "mcp_bot/__init__.py",
    "mcp_bot/client_runner.py",
    "mcp_bot/client.py",
    "mcp_bot/mcp_client_memory.json",
    "mcp_bot/server.py",
    "src/__init__.py",
    "src/both_plants_automation.py",
    "src/solar_automation.py",
    "src/wind_automation.py",
    "static/customer_data - Sheet1.csv",
    "static/logo_integrum.jpg",
    "static/both_csv_files_turbine/.gitkeep",
    "static/both_final_report/.gitkeep",
    "static/csv_files_turbine/.gitkeep",
    "static/plots_solar/.gitkeep",
    "static/solar_final_report/.gitkeep",
    "static/temp_csv/.gitkeep",
    "static/wind_final_report/.gitkeep",
    "templates/__init__.py",
    "templates/index.html",
    "templates/login.html",
    "whatsapp/__init__.py",
    "whatsapp/message_extraction.py",
    "whatsapp/sender_whatsapp.py"
]

for filepath in list_of_files:
    filepath = Path(filepath)
    filedir, filename = os.path.split(filepath)

    if filedir != "":
        os.makedirs(filedir, exist_ok=True)
        logging.info(f"Creating directory: {filedir} for the file: {filename}")

    if (not os.path.exists(filepath)) or (os.path.getsize(filepath) == 0):
        # For binary files like images, skip creating empty files
        if filename.lower().endswith(('.jpg', '.jpeg', '.png', '.gif')):
            if not os.path.exists(filepath):
                logging.info(f"Skipping creation of binary file placeholder: {filepath}")
            continue
        with open(filepath, "w") as f:
            pass
        logging.info(f"Creating empty file: {filepath}")
    else:
        logging.info(f"{filename} already exists")
