<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Admin Dashboard — User Management & Audit Logs</title>
  <link rel="icon" href="{{ url_for('static', filename='logo_integrum.jpg') }}" type="image/jpeg">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

  <style>
    :root {
      --primary: #0d6efd;
      --bg-light: #f8fafc;
      --card-bg: #fff;
      --border-color: #e9ecef;
      --text-dark: #222;
      --radius: 0.75rem;
      --transition: 0.25s ease;
    }

    body {
      font-family: "Inter", system-ui, -apple-system, "Segoe UI", <PERSON><PERSON>, sans-serif;
      background-color: var(--bg-light);
      color: #333;
      margin: 0;
      padding: 0;
      line-height: 1.5;
    }

    .container {
      max-width: 1200px;
      margin-top: 2rem;
    }

    h2 {
      font-weight: 700;
      color: var(--text-dark);
      letter-spacing: -0.3px;
    }

    /* --- Card --- */
    .card {
      border: none;
      border-radius: var(--radius);
      box-shadow: 0 4px 14px rgba(0,0,0,0.06);
      background: var(--card-bg);
      transition: box-shadow var(--transition);
    }

    .card:hover {
      box-shadow: 0 6px 20px rgba(0,0,0,0.08);
    }

    .card-header {
      background: linear-gradient(to right, #ffffff, #f9fafb);
      border-bottom: 1px solid var(--border-color);
      font-weight: 600;
      font-size: 1rem;
      color: #111;
    }

    /* --- Forms --- */
    .form-label {
      font-weight: 500;
      color: #555;
    }

    .form-control {
      border-radius: 0.5rem;
      box-shadow: none !important;
      border: 1px solid #d9e1ec;
      transition: border-color var(--transition), box-shadow var(--transition);
    }

    .form-control:focus {
      border-color: var(--primary);
      box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.15);
    }

    .btn {
      border-radius: 0.5rem;
      font-weight: 500;
      transition: all var(--transition);
    }

    .btn-primary {
      background: var(--primary);
      border: none;
    }

    .btn-primary:hover {
      background: #0b5ed7;
      transform: translateY(-1px);
    }

    .btn-outline-primary {
      border-width: 1.5px;
    }

    /* --- Table --- */
    .table {
      font-size: 0.92rem;
      margin-bottom: 0;
    }

    .table th {
      font-weight: 600;
      white-space: nowrap;
    }

    .table-hover tbody tr:hover {
      background-color: #f6f9fe;
    }

    .truncate {
      max-width: 350px;
      word-break: break-word;
      white-space: pre-wrap;
    }

    .table-responsive {
      max-height: 500px;
      overflow-y: auto;
      scrollbar-width: thin;
    }

    /* --- Pagination --- */
    .pagination .page-link {
      border-radius: 0.5rem;
      transition: background var(--transition);
    }

    .pagination .active .page-link {
      background: var(--primary);
      border-color: var(--primary);
    }

    /* --- Utility --- */
    @media (max-width: 768px) {
      .card-header form {
        flex-direction: column;
        align-items: stretch;
      }
      .card-header .col-auto {
        width: 100%;
      }
    }
  </style>
</head>
<body>
  <!-- Navbar with Logo (copied from index.html) -->
  <nav class="navbar navbar-expand-lg navbar-light" style="background-color: #fff; box-shadow: 0 2px 4px rgba(0,0,0,0.05); padding: 0.5rem 1.5rem;">
    <div class="container-fluid">
      <a class="navbar-brand" href="#">
        <img src="{{ url_for('static', filename='logo_integrum.jpg') }}" alt="Integrum Logo" style="height: 70px; width: auto;" />
      </a>
    </div>
  </nav>
<div class="container-fluid">
  <div class="row">
    <!-- Sidebar -->
    {% if not admin_login %}
    <nav id="sidebarMenu" class="col-md-2 d-md-block bg-light sidebar py-4" style="min-height: 100vh; border-radius: 0.75rem 0 0 0.75rem; box-shadow: 2px 0 8px rgba(0,0,0,0.03);">
      <div class="position-sticky">
        <ul class="nav flex-column">
          <li class="nav-item mb-2">
            <a class="nav-link active" href="#" id="menu-users" style="font-weight:600;">
              <span>👤 Users</span>
            </a>
            <ul class="nav flex-column ms-3" id="users-submenu" style="display:block;">
              <li class="nav-item">
                <a class="nav-link" href="#" id="submenu-view-users">👥 View Users</a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="#" id="submenu-create-user">➕ Create User</a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="#" id="submenu-delete-user">🗑️ Delete User</a>
              </li>
            </ul>
          </li>
          <li class="nav-item mb-2">
            <a class="nav-link" href="#" id="menu-plant-data" style="font-weight:600;">
              <span>🌱 Plant Data</span>
            </a>
          </li>
          <li class="nav-item mb-2">
            <a class="nav-link" href="#" id="menu-logs" style="font-weight:600;">
              <span>📜 Logs</span>
            </a>
          </li>
        </ul>
      </div>
    </nav>
    {% endif %}
    <!-- Main Content -->
    <main class="{% if not admin_login %}col-md-10 ms-sm-auto{% else %}col-12{% endif %} px-4">
      <h2 class="mb-4">⚡ Admin Dashboard</h2>

      {% if admin_login %}
      <!-- Admin Login Card -->
      <div class="card mx-auto mb-4" style="max-width: 420px;">
    <div class="card-header">Admin Login</div>
    <div class="card-body">
      {% if error %}
        <div class="alert alert-danger">{{ error }}</div>
      {% endif %}
      <form method="POST" action="{{ url_for('admin.admin_login') }}">
        <div class="mb-3">
          <label for="admin_email" class="form-label">Email</label>
          <input type="email" id="admin_email" name="email" class="form-control" required autocomplete="username">
        </div>
        <div class="mb-3">
          <label for="admin_password" class="form-label">Password</label>
          <input type="password" id="admin_password" name="password" class="form-control" required autocomplete="current-password">
        </div>
        <button type="submit" class="btn btn-primary w-100">Login</button>
      </form>
    </div>
  </div>

  {% else %}
  <!-- Plant Data Section -->
  <div id="plant-data-section" class="card mb-4">
    <div class="card-header">🌱 Plant Level Data</div>
    <div class="card-body">
      <form id="plantDataForm" class="row g-3 align-items-end">
        <div class="col-md-4">
          <label for="plantType" class="form-label">Plant Type</label>
          <select id="plantType" name="plantType" class="form-select" required>
            <option value="">Select Plant Type</option>
            <option value="wind">Wind</option>
            <option value="solar">Solar</option>
            <option value="both">Both</option>
          </select>
        </div>
        <div class="col-md-4">
          <label for="plantId" class="form-label">Plant ID</label>
          <input type="text" id="plantId" name="plantId" class="form-control" placeholder="Enter Plant ID" required>
        </div>
        <div class="col-md-4">
          <button type="submit" class="btn btn-primary">Fetch Data</button>
        </div>
      </form>
      <div id="plantDataResult" class="mt-4" style="display:none;">
        <h5>Important Plant Data</h5>
        <div id="plantDataContent"></div>
      </div>
    </div>
  </div>
  <!-- Users Section -->
  <div id="users-section">
    <!-- View Users -->
    <div id="view-users-section" class="card mb-4" style="display:none;">
      <div class="card-header">👥 All Users</div>
      <div class="card-body p-0">
        <div class="table-responsive">
          <table class="table table-striped table-hover align-middle mb-0">
            <thead class="table-light">
              <tr>
                <th>Username</th>
                <th>Email</th>
              </tr>
            </thead>
            <tbody>
              {% for user in users %}
              <tr>
                <td>{{ user.username }}</td>
                <td>{{ user.email }}</td>
              </tr>
              {% else %}
              <tr>
                <td colspan="2" class="text-center text-muted">No users found.</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <!-- Create User -->
    <div id="create-user-section" class="card mb-4">
      <div class="card-header">👤 Add New User</div>
      <div class="card-body">
        <form method="POST" action="{{ url_for('admin.admin_add_user') }}" class="row g-3">
          <div class="col-md-4">
            <label for="username" class="form-label">Username</label>
            <input type="text" id="username" name="username" class="form-control" required>
          </div>
          <div class="col-md-4">
            <label for="email" class="form-label">Email</label>
            <input type="email" id="email" name="email" class="form-control" required>
          </div>
          <div class="col-md-4">
            <label for="password" class="form-label">Password</label>
            <input type="password" id="password" name="password" class="form-control" required>
          </div>
          <div class="col-12 text-end">
            <button type="submit" class="btn btn-primary">➕ Add User</button>
          </div>
        </form>
      </div>
    </div>
    <!-- Delete User -->
    <div id="delete-user-section" class="card mb-4" style="display:none;">
      <div class="card-header">🗑️ Delete User</div>
      <div class="card-body">
        <form method="POST" action="{{ url_for('admin.admin_delete_user') }}" class="row g-3">
<div class="col-md-6">
            <label for="delete_email" class="form-label">Email</label>
            <input type="email" id="delete_email" name="email" class="form-control" required>
          </div>
          <div class="col-12 text-end">
            <button type="submit" class="btn btn-danger">🗑️ Delete User</button>
          </div>
        </form>
      </div>
    </div>
  </div>
  <!-- Logs Section -->
  <div id="logs-section" style="display:none;">
    <div class="card">
      <div class="card-header d-flex flex-wrap justify-content-between align-items-center">
        <span>📜 Audit Logs</span>
        <form id="logFilterForm" class="row g-2 align-items-center">
          <div class="col-auto">
            <select name="user" class="form-select form-select-sm" style="min-width:120px;">
              <option value="">All Users</option>
              {% for uname in log_usernames %}
                <option value="{{ uname }}">{{ uname }}</option>
              {% endfor %}
            </select>
          </div>
          <div class="col-auto">
            <select name="action_type" class="form-select form-select-sm" style="min-width:120px;">
              <option value="">All Actions</option>
              {% for act in log_action_types %}
                <option value="{{ act }}">{{ act }}</option>
              {% endfor %}
            </select>
          </div>
          <div class="col-auto"><input type="date" name="start_date" class="form-control form-control-sm"></div>
          <div class="col-auto"><input type="date" name="end_date" class="form-control form-control-sm"></div>
          <div class="col-auto"><button type="submit" class="btn btn-outline-primary btn-sm">Filter</button></div>
        </form>
      </div>
      <div class="card-body p-0">
        <div class="table-responsive">
          <table class="table table-striped table-hover align-middle">
            <thead class="table-light sticky-top">
              <tr>
                <th>Time</th>
                <th>User</th>
                <th>Action</th>
                <th>Target</th>
                <th>Details</th>
                <th>IP</th>
              </tr>
            </thead>
            <tbody id="auditLogTableBody">
              <tr><td colspan="6" class="text-center text-muted">Loading...</td></tr>
            </tbody>
          </table>
        </div>
        <nav aria-label="Audit log pagination">
          <ul class="pagination justify-content-center my-3" id="logPagination"></ul>
        </nav>
      </div>
    </div>
  </div>
  {% endif %}
</main>
</div>
</div>

{% if not admin_login %}
<script>
  // Plant Data AJAX logic
  $(function() {
    $('#plantDataForm').on('submit', function(e) {
      e.preventDefault();
      const plantType = $('#plantType').val();
      const plantId = $('#plantId').val();
      if (!plantType || !plantId) return;
      $('#plantDataResult').hide();
      $('#plantDataContent').html('<div class="text-muted">Loading...</div>');
      $.getJSON(`/admin/plant_data?type=${plantType}&id=${plantId}`, function(resp) {
        if (resp.success) {
          let html = '<ul class="list-group">';
          for (const [key, value] of Object.entries(resp.data)) {
            html += `<li class="list-group-item d-flex justify-content-between align-items-center">
              <span>${key}</span><span class="fw-bold">${value}</span>
            </li>`;
          }
          html += '</ul>';
          $('#plantDataContent').html(html);
        } else {
          $('#plantDataContent').html('<div class="text-danger">No data found or error occurred.</div>');
        }
        $('#plantDataResult').show();
      }).fail(function() {
        $('#plantDataContent').html('<div class="text-danger">Failed to fetch data.</div>');
        $('#plantDataResult').show();
      });
    });
  });

  // Sidebar navigation logic
  function showSection(section) {
    $('#users-section').hide();
    $('#logs-section').hide();
    $('#plant-data-section').hide();
    if (section === 'users') {
      $('#users-section').show();
      $('#create-user-section').show();
      $('#delete-user-section').hide();
    } else if (section === 'logs') {
      $('#logs-section').show();
    } else if (section === 'plant-data') {
      $('#plant-data-section').show();
    }
  }
  $(document).ready(function() {
    // Sidebar main menu
    $('#menu-users').on('click', function(e) {
      e.preventDefault();
      showSection('users');
      $('#menu-users').addClass('active');
      $('#menu-logs').removeClass('active');
      $('#menu-plant-data').removeClass('active');
      $('#users-submenu').show();
    });
    $('#menu-logs').on('click', function(e) {
      e.preventDefault();
      showSection('logs');
      $('#menu-logs').addClass('active');
      $('#menu-users').removeClass('active');
      $('#menu-plant-data').removeClass('active');
      $('#users-submenu').hide();
    });
    $('#menu-plant-data').on('click', function(e) {
      e.preventDefault();
      showSection('plant-data');
      $('#menu-plant-data').addClass('active');
      $('#menu-users').removeClass('active');
      $('#menu-logs').removeClass('active');
      $('#users-submenu').hide();
    });
    // Users submenu
    $('#submenu-view-users').on('click', function(e) {
      e.preventDefault();
      $('#view-users-section').show();
      $('#create-user-section').hide();
      $('#delete-user-section').hide();
    });
    $('#submenu-create-user').on('click', function(e) {
      e.preventDefault();
      $('#view-users-section').hide();
      $('#create-user-section').show();
      $('#delete-user-section').hide();
    });
    $('#submenu-delete-user').on('click', function(e) {
      e.preventDefault();
      $('#view-users-section').hide();
      $('#create-user-section').hide();
      $('#delete-user-section').show();
    });
    // Initial state: show create user by default
    showSection('users');
    $('#menu-users').addClass('active');
    $('#users-submenu').show();
    $('#view-users-section').hide();
    $('#create-user-section').show();
    $('#delete-user-section').hide();
    $('#plant-data-section').hide();
  });
</script>
{% endif %}

<script>
(() => {
  // Audit log logic (unchanged)
  const tbody = $('#auditLogTableBody');
  const pagination = $('#logPagination');

  const renderLogs = logs => {
    tbody.empty();
    if (!logs.length) {
      tbody.append('<tr><td colspan="6" class="text-center text-muted py-3">No logs found.</td></tr>');
      return;
    }
    logs.forEach(log => {
      tbody.append(`
        <tr>
          <td>${log.timestamp ? log.timestamp.replace('T', ' ').slice(0,19) : '-'}</td>
          <td>${log.username || '-'}</td>
          <td>${log.action_type || '-'}</td>
          <td>${log.target_type || ''}${log.target_id ? ' #' + log.target_id : ''}</td>
          <td class="truncate">${log.details || ''}</td>
          <td>${log.ip_address || '-'}</td>
        </tr>
      `);
    });
  };

  const renderPagination = (current, total) => {
    pagination.empty();
    if (total <= 1) return;
    for (let i = 1; i <= total; i++) {
      pagination.append(`
        <li class="page-item ${i === current ? 'active' : ''}">
          <a class="page-link" href="#" data-page="${i}">${i}</a>
        </li>
      `);
    }
  };

  const fetchAuditLogs = (page = 1) => {
    const query = $('#logFilterForm').serialize() + `&page=${page}`;
    $.getJSON('/admin/audit_logs?' + query, resp => {
      renderLogs(resp.logs);
      renderPagination(resp.page, Math.ceil(resp.total / resp.per_page));
    });
  };

  $('#logFilterForm').on('submit', e => {
    e.preventDefault();
    fetchAuditLogs(1);
  });

  pagination.on('click', 'a', e => {
    e.preventDefault();
    fetchAuditLogs($(e.target).data('page'));
  });

  $(document).ready(() => fetchAuditLogs(1));
})();
</script>
</body>
</html>
