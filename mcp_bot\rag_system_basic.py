import sys
import os

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
import pickle
from typing import List, Optional
from langchain.text_splitter import RecursiveCharacterTextSplitter
from openai import OpenAI
from dotenv import load_dotenv
import faiss
import numpy as np
from helper.logger_setup import setup_logger
from langchain_google_genai import GoogleGenerativeAIEmbeddings


load_dotenv()

# Set up logger
logger = setup_logger("rag_system", "rag_system.log")

# -----------------------------
# Helper Functions
# -----------------------------
def normalize_text(text: str) -> str:
    """Fix special characters and whitespace issues."""
    return text.replace("–", "-").replace("\xa0", " ").strip()

def simple_token_count(text: str) -> int:
    """Rough token count approximation for context assembly."""
    return len(text.split())

# -----------------------------
# Answer Generation
# -----------------------------
def generate_answer(query: str, context: str, openai_client: OpenAI) -> str:
    prompt = f"""
You are an expert AI assistant for Integrum Energy.
Answer the question using ONLY the context below.
If the answer is not in the context, politely say "I don't know".

Context:
{context}

Question: {query}
Answer:
"""
    response = openai_client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {"role": "system", "content": "You are a helpful and professional AI assistant."},
            {"role": "user", "content": prompt}
        ]
    )
    return response.choices[0].message.content

# -----------------------------
# Advanced RAG with FAISS
# -----------------------------
class RAGRetrieverFAISS:
    def __init__(self, persist_dir: str = "./faiss_index"):
        self.persist_dir = os.path.abspath(persist_dir)
        os.makedirs(self.persist_dir, exist_ok=True)
        self.index_file = os.path.join(self.persist_dir, "index.faiss")
        self.docs_file = os.path.join(self.persist_dir, "docs.pkl")
        self.meta_file = os.path.join(self.persist_dir, "meta.pkl")
        self.docs: List[str] = []
        self.metadata: List[dict] = []
        self.index: Optional[faiss.IndexFlatL2] = None
        self.embeddings: Optional[np.ndarray] = None
        self.openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        # self.gemini_client = GoogleGenerativeAIEmbeddings(model="models/embedding-001",
        #                                                   api_key=os.getenv("GEMINI_API_KEY"))


        

    # -------------------------
    # Load & split text files
    # -------------------------
    def ingest_file(self, txt_file: str, chunk_size: int = 500, chunk_overlap: int = 50):
        txt_file = os.path.abspath(txt_file)
        if not os.path.exists(txt_file):
            raise FileNotFoundError(f"File not found: {txt_file}")

        with open(txt_file, "r", encoding="utf-8") as f:
            text = normalize_text(f.read())

        splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap
        )
        chunks = [normalize_text(d) for d in splitter.split_text(text)]

        for i, chunk in enumerate(chunks):
            self.docs.append(chunk)
            self.metadata.append({
                "source": txt_file,
                "chunk_id": f"{os.path.basename(txt_file)}_chunk_{i}"
            })
        logger.info(f"Ingested {len(chunks)} chunks from {txt_file}")

    # -------------------------
    # Generate embeddings
    # -------------------------
    def get_embedding(self, text: str) -> np.ndarray:
        response = self.openai_client.embeddings.create(
            model="text-embedding-3-small",
            input=text
        )
        return np.array(response.data[0].embedding, dtype=np.float32)


    # def get_embedding(self, text: str) -> np.ndarray:
    #         embedding = self.gemini_client.embed_query(text)
    #         return np.array(embedding, dtype=np.float32)
    

    # -------------------------
    # Create or load FAISS index
    # -------------------------
    def build_or_load_index(self):
        dim = 1536  # text-embedding-3-small

        if os.path.exists(self.index_file) and os.path.exists(self.docs_file) and os.path.exists(self.meta_file):
            self.index = faiss.read_index(self.index_file)
            with open(self.docs_file, "rb") as f:
                self.docs = pickle.load(f)
            with open(self.meta_file, "rb") as f:
                self.metadata = pickle.load(f)
            logger.info(f"Loaded existing FAISS index with {len(self.docs)} documents.")
        else:
            self.embeddings = np.array([self.get_embedding(d) for d in self.docs], dtype=np.float32)
            self.index = faiss.IndexFlatL2(dim)
            self.index.add(self.embeddings)
            faiss.write_index(self.index, self.index_file)
            with open(self.docs_file, "wb") as f:
                pickle.dump(self.docs, f)
            with open(self.meta_file, "wb") as f:
                pickle.dump(self.metadata, f)
            logger.info(f"Created new FAISS index with {len(self.docs)} documents.")

    # -------------------------
    # Incremental document addition
    # -------------------------
    def add_documents_incremental(self, new_docs: List[str], source_name: str):
        new_embeddings = np.array([self.get_embedding(d) for d in new_docs], dtype=np.float32)
        self.index.add(new_embeddings)

        for i, doc in enumerate(new_docs):
            self.docs.append(doc)
            self.metadata.append({
                "source": source_name,
                "chunk_id": f"{source_name}_chunk_{len(self.docs)-1}"
            })

        # Persist
        faiss.write_index(self.index, self.index_file)
        with open(self.docs_file, "wb") as f:
            pickle.dump(self.docs, f)
        with open(self.meta_file, "wb") as f:
            pickle.dump(self.metadata, f)
        logger.info(f"Added {len(new_docs)} new documents incrementally.")

    # -------------------------
    # Retrieve top-k relevant chunks
    # -------------------------
    def retrieve(self, query: str, k: int = 5, keywords: List[str] = None) -> List[str]:
        query_emb = self.get_embedding(query).reshape(1, -1)
        distances, indices = self.index.search(query_emb, k)
        docs = [self.docs[i] for i in indices[0] if i < len(self.docs)]

        # Metadata/keyword filtering
        if keywords:
            keywords_lower = [kw.lower() for kw in keywords]
            docs = [doc for doc in docs if any(kw in doc.lower() for kw in keywords_lower)]

        if not docs:
            logger.warning("No relevant documents found for the query.")
        return docs

    # -------------------------
    # Assemble context for LLM
    # -------------------------
    def assemble_context(self, docs: List[str], max_tokens: int = 2500) -> str:
        context = ""
        tokens = 0
        for doc in docs:
            t_count = simple_token_count(doc)
            if tokens + t_count > max_tokens:
                break
            context += doc + "\n"
            tokens += t_count
        return context.strip()
    
    def list_documents(self, limit: int = 20):
        logger.info(f"Total documents: {len(self.docs)}")
        for i, meta in enumerate(self.metadata[:limit]):
            logger.info(f"ID: {i}, Source: {meta['source']}, Chunk ID: {meta['chunk_id']}")

    def view_document(self, doc_id: int):
        if doc_id < 0 or doc_id >= len(self.docs):
            logger.error(f"Invalid document ID: {doc_id}")
            return
        logger.info(f"Document ID: {doc_id}")
        logger.info(f"Source: {self.metadata[doc_id]['source']}")
        logger.info(f"Chunk ID: {self.metadata[doc_id]['chunk_id']}")
        logger.info("Content:")
        logger.info(self.docs[doc_id])


    def delete_document(self, doc_id: int):
        if doc_id < 0 or doc_id >= len(self.docs):
            logger.error(f"Invalid document ID: {doc_id}")
            return
        
        # Remove document and metadata
        self.docs.pop(doc_id)
        self.metadata.pop(doc_id)
        
        # Rebuild embeddings and FAISS index
        if len(self.docs) > 0:
            embeddings = np.array([self.get_embedding(d) for d in self.docs], dtype=np.float32)
            dim = embeddings.shape[1]
            self.index = faiss.IndexFlatL2(dim)
            self.index.add(embeddings)
        else:
            self.index = None  # empty index
        
        # Persist updated data
        faiss.write_index(self.index, self.index_file)
        with open(self.docs_file, "wb") as f:
            pickle.dump(self.docs, f)
        with open(self.meta_file, "wb") as f:
            pickle.dump(self.metadata, f)
        
        logger.info(f"Document ID {doc_id} deleted and index updated.")




# -----------------------------
# Example Usage
# -----------------------------
if __name__ == "__main__":
    rag = RAGRetrieverFAISS()

    # Ingest initial txt file
    txt_path = os.path.join("mcp_bot", "integrum_energy.txt")
    rag.ingest_file(txt_path)

    # Build or load FAISS index
    rag.build_or_load_index()

    # Example query
    query = "Who is the COO of Integrum Energy?"
    top_docs = rag.retrieve(query, k=5, keywords=["CEO", "Founder", "Leadership"])
    context = rag.assemble_context(top_docs)

    if top_docs:
        answer = generate_answer(query, context, rag.openai_client)
        logger.info(f"\n[ANSWER]: {answer}")
    else:
        logger.info("\n[ANSWER]: I don't know")
    # rag = RAGRetrieverFAISS()
    # rag.build_or_load_index()

    # # List first 10 chunks
    # rag.list_documents(limit=10)

    # # View a specific chunk
    # rag.view_document(doc_id=2)

    # # Delete a chunk
    # rag.delete_document(doc_id=2)

    # # Incremental add new docs
    # new_docs = ["New paragraph about Integrum Energy IPO.", "Another new section on awards."]
    # rag.add_documents_incremental(new_docs, source_name="new_docs.txt")