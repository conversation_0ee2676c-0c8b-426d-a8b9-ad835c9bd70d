"""
advanced_mcp_client.py

An advanced, production-oriented MCP client implementing:
 - Transport abstraction (stdio and HTTP/SSE)
 - Session manager with health checks, reconnect/backoff, per-session state
 - Dynamic tool discovery with caching & versioning
 - Tool adapters loader (langchain_mcp_adapters compatible)
 - Tracing integration with LangSmith (ls.tracing_context)
 - Tool output JSON Schema validation (jsonschema)
 - Allowlist / denylist for tools
 - Per-server concurrency limits and timeouts
 - Prometheus metrics endpoint for observability
 - Minimal test harness / CLI to connect to a local stdio or HTTP MCP server

Notes:
 - Requires Python 3.9+
 - Install dependencies:
     pip install mcp langchain-mcp-adapters langsmith langchain aiohttp jsonschema prometheus-client
 - This file is intended as a single-file advanced example to drop into your project.
 - Adapt paths, auth and provider logic to your environment (LLM provider, LangSmith keys, etc).
"""

import asyncio
import json
import logging
import os
import time
import uuid
from dataclasses import dataclass, field
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Tuple

# MCP SDK & adapters
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from langchain_mcp_adapters.tools import load_mcp_tools  # adapter helper
# If using HTTP/SSE transport from the MCP SDK, adapt accordingly (some SDKs provide http_client wrappers).
# We'll implement a minimal HTTP client wrapper using aiohttp (streaming SSE).
import aiohttp

# Tracing
import langsmith as ls

# JSON Schema validation
from jsonschema import validate as jsonschema_validate, ValidationError

# Prometheus metrics
from prometheus_client import start_http_server, Counter, Gauge, Histogram

# Concurrency primitives
from asyncio import Semaphore

# Logging setup
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("advanced_mcp_client")

# ---- Prometheus metrics ----
MET_TOOL_INVOCATIONS = Counter("mcp_tool_invocations_total", "Total MCP tool invocations", ["server", "tool"])
MET_TOOL_ERRORS = Counter("mcp_tool_errors_total", "Total MCP tool errors", ["server", "tool"])
MET_TOOL_LATENCY = Histogram("mcp_tool_latency_seconds", "Latency per tool invocation", ["server", "tool"])
MET_SESSION_HEALTH = Gauge("mcp_session_health", "1 healthy, 0 unhealthy", ["server"])
MET_ACTIVE_SESSIONS = Gauge("mcp_active_sessions", "Active MCP client sessions")

# ---- Utilities ----
def iso_now():
    return datetime.now(timezone.utc).isoformat()

def jitter(max_jitter=0.3):
    return (max_jitter * (2 * (os.urandom(1)[0] / 255.0) - 1))

# ---- Configurable dataclasses ----
@dataclass
class MCPServerSpec:
    """Describe an MCP server to connect to."""
    id: str
    path: str  # local script path (for stdio) or http(s) base url (for http)
    transport: str = "stdio"  # 'stdio' or 'http'
    args: Optional[List[str]] = None
    auth: Optional[Dict[str, Any]] = None  # e.g. {"bearer_token": "..."}
    max_concurrency: int = 4
    health_endpoint: Optional[str] = None  # for HTTP servers
    tool_allowlist: Optional[List[str]] = None  # if set, only allow these tools
    tool_denylist: Optional[List[str]] = None


@dataclass
class SessionState:
    session: Optional[ClientSession] = None
    spec: Optional[MCPServerSpec] = None
    last_ping: float = 0.0
    fail_count: int = 0
    healthy: bool = False
    semaphore: Semaphore = field(default_factory=lambda: Semaphore(4))
    tools_cache: Dict[str, Dict] = field(default_factory=dict)  # tool_name -> descriptor
    tools_version_tag: Optional[str] = None  # ETag-like versioning
    last_discovered: float = 0.0


# ---- Transport factory & HTTP wrapper ----
class TransportFactory:
    """Create connections to MCP servers supporting stdio and HTTP (SSE)."""

    @staticmethod
    async def connect_stdio(spec: MCPServerSpec, exit_stack):
        """
        Launch a stdio client for a local MCP server script.
        Returns (read, write) streams as provided by stdio_client.
        """
        params = StdioServerParameters(command="python" if spec.path.endswith(".py") else "node",
                                       args=[spec.path] + (spec.args or []))
        logger.info("[TRANSPORT] Connecting to stdio server: %s", spec.path)
        read, write = await exit_stack.enter_async_context(stdio_client(params))
        return read, write

    @staticmethod
    async def connect_http_session(spec: MCPServerSpec, session: aiohttp.ClientSession):
        """
        Connect to HTTP/SSE MCP server. This returns a wrapper object with `.post` and `.get_stream`
        for sending messages. We're not implementing full MCP HTTP client; this wrapper helps
        to call server-specific endpoints (list_tools, invoke, etc). The exact server API paths
        depend on the MCP server implementation - adjust as needed.
        """
        base = spec.path.rstrip("/")
        class HTTPWrapper:
            def __init__(self, base, session, auth):
                self.base = base
                self.session = session
                self.auth = auth

            def _headers(self):
                h = {"Accept": "application/json"}
                if self.auth and "bearer_token" in self.auth:
                    h["Authorization"] = f"Bearer {self.auth['bearer_token']}"
                return h

            async def list_tools(self):
                url = f"{self.base}/.well-known/mcp/tools"
                async with self.session.get(url, headers=self._headers(), timeout=10) as resp:
                    return await resp.json()

            async def invoke_tool(self, tool_name: str, payload: dict, timeout=30):
                url = f"{self.base}/invoke/{tool_name}"
                async with self.session.post(url, json=payload, headers=self._headers(), timeout=timeout) as resp:
                    # Best-effort: attempt to parse streaming JSON lines or a final JSON
                    text = await resp.text()
                    try:
                        return json.loads(text)
                    except Exception:
                        return {"raw": text, "status": resp.status}

            async def stream_invoke(self, tool_name: str, payload: dict):
                # SSE / streaming invocation: open streaming endpoint and yield events
                url = f"{self.base}/stream-invoke/{tool_name}"
                async with self.session.get(url, headers=self._headers(), timeout=None) as resp:
                    async for line in resp.content:
                        yield line

        return HTTPWrapper(base, session, spec.auth)


# ---- Session Manager ----
class SessionManager:
    """
    Manage connections (ClientSession wrappers) for multiple MCP server specs.
    Each managed session (stdio) is a full ClientSession from the MCP SDK.
    For HTTP servers we provide an HTTP wrapper object that exposes a compatible API.
    """

    def __init__(self, specs: List[MCPServerSpec], ls_project: Optional[str] = None):
        self.specs = specs
        self.sessions: Dict[str, SessionState] = {}
        self.exit_stack = None  # created per connect() call (AsyncExitStack)
        self.ls_project = ls_project

    async def connect_all(self):
        self.exit_stack = asyncio.get_event_loop().run_until_complete(asyncio.to_thread(asyncio.new_event_loop)) \
            if False else asyncio.AsyncExitStack()  # keep typing happy; we will create proper AsyncExitStack below

        self.exit_stack = asyncio.AsyncExitStack()
        await self.exit_stack.__aenter__()
        # create a single aiohttp client for HTTP transports
        aiohttp_client = aiohttp.ClientSession()
        for spec in self.specs:
            state = SessionState(spec=spec, semaphore=Semaphore(spec.max_concurrency))
            self.sessions[spec.id] = state
        # Connect concurrently
        tasks = [self._connect_one(spec, aiohttp_client) for spec in self.specs]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        # mark active sessions metric
        MET_ACTIVE_SESSIONS.set(len([s for s in self.sessions.values() if s.session]))
        return results

    async def _connect_one(self, spec: MCPServerSpec, aiohttp_client: aiohttp.ClientSession):
        state = self.sessions[spec.id]
        backoff_base = 1.0
        for attempt in range(6):  # retry attempts
            try:
                if spec.transport == "stdio":
                    read, write = await self.exit_stack.enter_async_context(stdio_client(StdioServerParameters(
                        command="python" if spec.path.endswith(".py") else "node",
                        args=[spec.path] + (spec.args or [])
                    )))
                    # wrap the returned read/write with a ClientSession
                    session = await self.exit_stack.enter_async_context(ClientSession(read, write))
                    await session.initialize()
                    state.session = session
                    state.healthy = True
                    state.last_ping = time.time()
                    logger.info("[SESSION] Connected stdio session for %s", spec.id)
                    MET_SESSION_HEALTH.labels(server=spec.id).set(1)
                    return session
                else:
                    # HTTP transport
                    http_wrapper = await TransportFactory.connect_http_session(spec, aiohttp_client)
                    state.session = http_wrapper
                    state.healthy = True
                    state.last_ping = time.time()
                    logger.info("[SESSION] Connected HTTP session for %s -> %s", spec.id, spec.path)
                    MET_SESSION_HEALTH.labels(server=spec.id).set(1)
                    return http_wrapper
            except Exception as e:
                state.fail_count += 1
                backoff = min(backoff_base * (2 ** attempt) + abs(jitter()), 30)
                logger.warning("[SESSION] Failed to connect to %s (attempt %d): %s. Backing off %.2fs",
                               spec.id, attempt + 1, e, backoff)
                MET_SESSION_HEALTH.labels(server=spec.id).set(0)
                await asyncio.sleep(backoff)
        # If we exit loop, mark unhealthy
        state.healthy = False
        logger.error("[SESSION] Could not connect to %s after retries", spec.id)
        return None

    async def close(self):
        if self.exit_stack:
            try:
                await self.exit_stack.aclose()
            except Exception as e:
                logger.warning("[SESSION] Error closing exit stack: %s", e)
        # close aiohttp client if any sessions used it
        # (We didn't store aiohttp client reference globally; in more complete code store and close it)

    def healthy_sessions(self) -> List[SessionState]:
        return [s for s in self.sessions.values() if s.healthy and s.session is not None]


# ---- MCP Multi Server Client (main functionality) ----
class MCPMultiServerClient:
    """
    High-level client that:
     - connects to multiple MCP servers (stdio & HTTP)
     - discovers tools (with cache + normalization)
     - validates outputs with JSON schemas
     - invokes tools with concurrency control, tracing, metrics
     - exposes helper for LangChain/LangGraph adapter loading
    """

    def __init__(self, specs: List[MCPServerSpec], langsmith_project: str = "MCP-Client", tracing_enabled: bool = True):
        self.specs = specs
        self.session_manager = SessionManager(specs, ls_project=langsmith_project)
        self.langsmith_project = langsmith_project
        self.tracing_enabled = tracing_enabled
        self.thread_id = str(uuid.uuid4())
        # In-memory tool registry across servers: (server_id, tool_name) -> descriptor
        self.tool_registry: Dict[Tuple[str, str], Dict[str, Any]] = {}
        # Default tool allowlist & denylist global (can be overridden per spec)
        self.global_allowlist: Optional[List[str]] = None
        self.global_denylist: Optional[List[str]] = None

    async def connect(self):
        """Connect all sessions and auto-discover tools."""
        await self.session_manager.connect_all()
        # After sessions are connected, discover tools
        await self.discover_all_tools()

    async def discover_all_tools(self, force: bool = False):
        """Discover tools from each healthy session, normalize descriptors, and cache them."""
        states = self.session_manager.healthy_sessions()
        tasks = [self._discover_tools_for_state(s, force=force) for s in states]
        await asyncio.gather(*tasks, return_exceptions=True)

    async def _discover_tools_for_state(self, state: SessionState, force: bool = False):
        spec = state.spec
        server_id = spec.id
        try:
            # If cached recently and not forced, skip
            now = time.time()
            if not force and (now - state.last_discovered) < 60 and state.tools_cache:
                logger.debug("[DISCOVER] Using cached tools for %s", server_id)
                return state.tools_cache

            logger.info("[DISCOVER] Loading tools from server %s (transport=%s)", server_id, spec.transport)
            tools_raw = None
            if spec.transport == "stdio":
                # Use ClientSession.list_tools() from MCP SDK
                if not isinstance(state.session, ClientSession):
                    logger.warning("[DISCOVER] session for %s not a ClientSession", server_id)
                    return {}
                tools_raw = await state.session.list_tools()
            else:
                # HTTP wrapper
                tools_raw = await state.session.list_tools()

            # tools_raw expected to be a list of descriptors; normalize
            normalized = {}
            for t in tools_raw:
                name = t.get("name") or t.get("id") or t.get("tool_name")
                if not name:
                    continue
                normalized[name] = {
                    "name": name,
                    "description": t.get("description", ""),
                    "schema": t.get("schema") or t.get("response_schema") or t.get("output_schema"),
                    "raw": t
                }

            # Apply allowlist/denylist per-spec or global
            final_tools = {}
            for name, desc in normalized.items():
                if spec.tool_allowlist and name not in spec.tool_allowlist:
                    continue
                if spec.tool_denylist and name in spec.tool_denylist:
                    continue
                if self.global_allowlist and name not in self.global_allowlist:
                    continue
                if self.global_denylist and name in self.global_denylist:
                    continue
                final_tools[name] = desc
                # Global registry key
                self.tool_registry[(server_id, name)] = desc

            # Update state
            state.tools_cache = final_tools
            state.last_discovered = time.time()
            logger.info("[DISCOVER] %d tools discovered from %s", len(final_tools), server_id)
            return final_tools
        except Exception as e:
            logger.error("[DISCOVER] Failed to discover tools from %s: %s", server_id, e)
            state.healthy = False
            MET_SESSION_HEALTH.labels(server=server_id).set(0)
            return {}

    async def get_tool_descriptor(self, server_id: str, tool_name: str) -> Optional[Dict[str, Any]]:
        return self.tool_registry.get((server_id, tool_name))

    async def invoke_tool(self, server_id: str, tool_name: str, args: dict, timeout: int = 30) -> dict:
        """
        Invoke a named tool on a designated server with:
         - concurrency controls (per-server semaphore)
         - tracing context for LangSmith
         - JSON Schema validation (if descriptor contains schema)
         - metrics and error handling
        """
        state = self.session_manager.sessions.get(server_id)
        if not state or not state.healthy:
            raise RuntimeError(f"Server {server_id} not available")

        spec = state.spec
        descriptor = await self.get_tool_descriptor(server_id, tool_name)
        if descriptor is None:
            raise RuntimeError(f"Tool {tool_name} not found on server {server_id}")

        # Acquire semaphore
        async with state.semaphore:
            MET_TOOL_INVOCATIONS.labels(server=server_id, tool=tool_name).inc()
            start_ts = time.time()
            with MET_TOOL_LATENCY.labels(server=server_id, tool=tool_name).time():
                # Optionally trace
                trace_tags = {"server": server_id, "tool": tool_name, "thread_id": self.thread_id}
                if self.tracing_enabled:
                    ctx = ls.tracing_context(project_name=self.langsmith_project, tags=["mcp-client-invoke", tool_name], metadata=trace_tags)
                else:
                    ctx = ls.tracing_context(enabled=False)

                with ctx:
                    try:
                        logger.debug("[INVOKE] Invoking %s on %s with args=%s", tool_name, server_id, args)
                        if spec.transport == "stdio":
                            # Use MCP Python SDK: ClientSession.call(...)
                            # The SDK call semantics may vary; we use `call_tool` as a plausible method.
                            # Adjust to actual SDK method (e.g., session.call(tool_name, args))
                            cs: ClientSession = state.session  # type: ignore
                            # Many MCP SDKs provide session.call(tool_name, args)
                            result = await cs.call(tool_name, args, timeout=timeout)
                        else:
                            result = await state.session.invoke_tool(tool_name, {"args": args}, timeout=timeout)  # type: ignore

                        # Validate schema if present
                        schema = descriptor.get("schema")
                        if schema:
                            try:
                                jsonschema_validate(instance=result, schema=schema)
                            except ValidationError as ve:
                                MET_TOOL_ERRORS.labels(server=server_id, tool=tool_name).inc()
                                logger.warning("[VALIDATION] Tool %s produced output that failed schema: %s", tool_name, ve)
                                # Return an error wrapper rather than raw output
                                return {"error": "tool_output_validation_failed", "details": str(ve), "raw_output": result}

                        elapsed = time.time() - start_ts
                        logger.info("[INVOKE] Tool %s@%s completed in %.3fs", tool_name, server_id, elapsed)
                        return {"result": result, "elapsed": elapsed}
                    except Exception as e:
                        MET_TOOL_ERRORS.labels(server=server_id, tool=tool_name).inc()
                        logger.error("[INVOKE] Error invoking tool %s@%s: %s", tool_name, server_id, e)
                        raise

    async def load_langchain_tools_for_server(self, server_id: str):
        """
        Helper to load MCP tools as LangChain tools using langchain_mcp_adapters.
        This method expects the underlying session is a ClientSession (stdio).
        """
        state = self.session_manager.sessions.get(server_id)
        if state is None:
            return []
        if not isinstance(state.session, ClientSession):
            logger.warning("[ADAPTER] Server %s transport is not stdio; langchain adapters may not support it.", server_id)
            return []
        try:
            maybe = load_mcp_tools(state.session)
            tools = await maybe if asyncio.iscoroutine(maybe) else maybe
            logger.info("[ADAPTER] Converted %d tools from %s to LangChain tools", len(tools), server_id)
            return tools
        except Exception as e:
            logger.error("[ADAPTER] Failed to load langchain tools for %s: %s", server_id, e)
            return []

    def set_global_allowdeny(self, allowlist: Optional[List[str]] = None, denylist: Optional[List[str]] = None):
        self.global_allowlist = allowlist
        self.global_denylist = denylist

    async def refresh_tools_if_stale(self):
        """Periodically discover tools if stale (used by background task)."""
        while True:
            try:
                await self.discover_all_tools()
            except Exception as e:
                logger.warning("[REFRESH] Error during periodic tool discovery: %s", e)
            await asyncio.sleep(60)

# ---- Minimal CLI/test harness ----
async def main_cli():
    """
    Example usage:
      - Connect to a local stdio MCP server script: python advanced_mcp_client.py --stdio-script ./examples/my_mcp_server.py
      - Connect to an HTTP MCP server: python advanced_mcp_client.py --http-url http://localhost:8000
    For simplicity this CLI expects either stdio path or http url and will try to connect, discover tools and invoke one example tool.
    """
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument("--stdio-script", type=str, help="path to local MCP server script (stdio)")
    parser.add_argument("--http-url", type=str, help="base URL to HTTP MCP server (http)")
    parser.add_argument("--tool", type=str, help="tool name to invoke (optional)", default=None)
    parser.add_argument("--args", type=str, help="JSON args to pass to tool", default="{}")
    parser.add_argument("--prom-port", type=int, default=9100, help="Prometheus metrics port")
    args = parser.parse_args()

    specs = []
    if args.stdio_script:
        specs.append(MCPServerSpec(id="local-stdio", path=args.stdio_script, transport="stdio", max_concurrency=4))
    if args.http_url:
        specs.append(MCPServerSpec(id="http-1", path=args.http_url, transport="http", max_concurrency=8))

    if not specs:
        parser.error("Provide --stdio-script or --http-url to connect to a server.")

    # Start Prometheus metrics server
    start_http_server(args.prom_port)
    logger.info("[METRICS] Prometheus metrics available on port %d", args.prom_port)

    # Create client
    client = MCPMultiServerClient(specs, langsmith_project="Advanced-MCP-Client", tracing_enabled=True)

    # Connect
    await client.connect()

    # Start background tool refresh
    asyncio.create_task(client.refresh_tools_if_stale())

    # If user provided a tool to invoke, attempt to find it and invoke on the first server that exposes it
    if args.tool:
        parsed_args = json.loads(args.args or "{}")
        # find server that exposes the tool
        found = None
        for (srv, tname), desc in client.tool_registry.items():
            if tname == args.tool:
                found = (srv, desc)
                break
        if not found:
            logger.error("Tool %s not found across connected servers", args.tool)
            return
        server_id, desc = found
        logger.info("Invoking tool %s on server %s with args=%s", args.tool, server_id, parsed_args)
        try:
            out = await client.invoke_tool(server_id, args.tool, parsed_args)
            logger.info("Invocation output: %s", out)
        except Exception as e:
            logger.error("Invocation failed: %s", e)
    else:
        # If no tool specified, print discovered tool list
        logger.info("Discovered tools registry (server -> tools):")
        by_server = {}
        for (srv, tname), desc in client.tool_registry.items():
            by_server.setdefault(srv, []).append(tname)
        for srv, tools in by_server.items():
            logger.info("  %s: %s", srv, ", ".join(tools))

    # keep running to allow metrics scraping & background refresh
    while True:
        await asyncio.sleep(300)


# ---- Entrypoint ----
if __name__ == "__main__":
    # Run the CLI event loop
    try:
        asyncio.run(main_cli())
    except KeyboardInterrupt:
        logger.info("Exiting on user interrupt.")
