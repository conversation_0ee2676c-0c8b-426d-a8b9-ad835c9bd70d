@echo off
REM ==============================
REM  DGR Project - Run Script
REM ==============================

echo ----------------------------------------
echo   Activating virtual environment...
echo ----------------------------------------
call venv\Scripts\activate

if errorlevel 1 (
    echo Failed to activate virtual environment. Please run setup.bat first.
    pause
    exit /b 1
)

echo ----------------------------------------
echo   Starting DGR Project...
echo ----------------------------------------
python main.py

echo ----------------------------------------
echo   Application stopped.
echo ----------------------------------------
pause
