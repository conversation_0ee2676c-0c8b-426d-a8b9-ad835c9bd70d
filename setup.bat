@echo off
REM ==============================
REM  DGR Project - Setup Script
REM ==============================

echo ----------------------------------------
echo   Creating virtual environment...
echo ----------------------------------------
python -m venv venv

if errorlevel 1 (
    echo Failed to create virtual environment.
    pause
    exit /b 1
)

echo ----------------------------------------
echo   Activating virtual environment...
echo ----------------------------------------
call venv\Scripts\activate

echo ----------------------------------------
echo   Installing dependencies...
echo ----------------------------------------
pip install --upgrade pip
pip install -r requirements.txt

if errorlevel 1 (
    echo Failed to install dependencies.
    pause
    exit /b 1
)

echo ----------------------------------------
echo   Setup complete! You can now run the project with run_project.bat
echo ----------------------------------------
pause
