import matplotlib
matplotlib.use('Agg') # Use non-interactive backend to avoid GUI issues
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_pdf import PdfPages
import matplotlib.image as mpimg
import math
from PyPDF2 import PdfMerger
from helper.logger_setup import setup_logger

logger = setup_logger('wind_plots', 'wind_plots.log')




# Final plot for wind speed for last 30 days
def plot_wind_speed_last_30_days(dataframe, plant_name, date_col="time", title="Wind Speed Over Last 30 Days"):
    """
    Plots wind speed trends over the last 30 days with an average line.
    """
    try:
        logger.info("Starting plot_wind_speed function")

        if date_col not in dataframe.columns:
            raise ValueError(f"'{date_col}' column not found in the dataframe.")

        # Select wind speed columns (all columns except the date column)
        wind_cols = [col for col in dataframe.columns if col != date_col]
        if not wind_cols:
            raise ValueError("No wind speed data found in the dataframe.")

        # Convert date column to datetime and drop invalid dates
        df = dataframe.copy()
        df[date_col] = pd.to_datetime(df[date_col], errors='coerce')
        df = df.dropna(subset=[date_col])
        df[date_col] = df[date_col].dt.tz_localize(None)

        # Filter last 30 days
        last_30_days = df[date_col].max() - pd.Timedelta(days=30)
        df = df[df[date_col] >= last_30_days]

        if df.empty:
            raise ValueError("No data available for the last 30 days.")

        plt.figure(figsize=(12, 6))

        # Plot individual wind speed lines
        for col in wind_cols:
            plt.plot(df[date_col], df[col], marker='o', linestyle='-', label=col)

        # Plot average line
        average_wind_speed = df[wind_cols].mean(axis=1).mean()
        plt.axhline(average_wind_speed, color='red', linestyle='dashed', linewidth=2, label=f"Avg Wind Speed: {average_wind_speed:.2f} m/s")

        # Add value labels (adjusted for better positioning)
        for i in range(len(df)):
            avg_speed = df[wind_cols].iloc[i].mean()
            plt.text(df[date_col].iloc[i], avg_speed + 0.2, f"{avg_speed:.2f}",
                     ha='center', fontsize=8, color='black', rotation=30)

        # Labels and styling
        plt.title(title, fontsize=16, weight='bold', pad=20)
        plt.xlabel("Date", fontsize=12)
        plt.ylabel("Wind Speed (m/s)", fontsize=12)
        plt.xticks(rotation=45, fontsize=10)
        plt.yticks(fontsize=10)
        plt.legend(fontsize=10)
        plt.grid(axis='y', linestyle='--', alpha=0.7)

        # Save plot
        output_filename = f"{plant_name}_wind_speed_plot.png"
        plt.savefig(output_filename, dpi=300, bbox_inches='tight')
        plt.close()

        logger.info(f"Wind speed plot saved to {output_filename}")
        return output_filename

    except Exception as e:
        logger.error(f"Error in plot_wind_speed: {e}")
        raise



# Final plot for Generation for last 30 days
def plot_daily_generation_last_30_days(dataframe, plant_name, date_col="time"):
    """
    Plots daily generation over the last 30 days for all turbines in a single plot and saves it as an image.
    """
    try:
        # Convert date column to datetime
        dataframe[date_col] = pd.to_datetime(dataframe[date_col], errors='coerce')
        dataframe = dataframe.dropna(subset=[date_col])
        dataframe[date_col] = dataframe[date_col].dt.tz_localize(None)

        # Filter last 30 days data
        last_30_days = dataframe[date_col].max() - pd.DateOffset(days=30)
        df = dataframe[dataframe[date_col] >= last_30_days]

        if df.empty:
            raise ValueError("No data available for the last 30 days.")

        plt.figure(figsize=(18, 8))

        # Plot each turbine's daily generation
        for i, col in enumerate(df.columns[1:]):
            plt.plot(df[date_col], df[col], marker='o', label=f'Turbine {i+1}')

            # Add value labels on the plot
            for x, y in zip(df[date_col], df[col]):
                plt.text(x, y, f"{y:.2f}", ha='center', va='bottom', fontsize=8, rotation=30)

        plt.title("Daily Generation Over Last 30 Days", fontsize=28, weight='bold', pad=20)

        plt.xlabel("Date", fontsize=12)
        plt.ylabel("Generation (kWh)", fontsize=12)
        plt.xticks(rotation=45)
        plt.legend(fontsize=10)
        plt.grid(axis='y', linestyle='--', alpha=0.7)
        plt.tight_layout()

        output_image=f"{plant_name}_daily_generation_last_30_days.png"

        # Save the plot as an image
        plt.savefig(output_image, dpi=300)

        print(f"Plot saved as {output_image}")
        return output_image

    except Exception as e:
        print(f"Error: {e}")



def generate_wind_plot(wind_plot, generation_plot, pdf_filename):
    logger.info("Starting to generate wind plot PDF...")

    try:
        with PdfPages(pdf_filename) as pdf:
            # Create a figure for A4 size (8.27 x 11.69 inches)
            fig, axes = plt.subplots(2, 1, figsize=(8.27, 11.69), gridspec_kw={'height_ratios': [1, 1]})

            # Load and plot wind speed plot
            try:
                logger.info(f"Loading image: {wind_plot}")
                wind_img = mpimg.imread(wind_plot)
                axes[0].imshow(wind_img)
                axes[0].axis("off")


            except FileNotFoundError:
                logger.error(f"File not found: {wind_plot}")
                axes[0].text(0.5, 0.5, "Wind plot not found", ha='center', va='center', fontsize=12, color='red')

            except Exception as e:
                logger.error(f"Error loading wind plot {wind_plot}: {e}")
                axes[0].text(0.5, 0.5, f"Error: {e}", ha='center', va='center', fontsize=12, color='red')

            # Load and plot generation plot
            try:
                logger.info(f"Loading image: {generation_plot}")
                gen_img = mpimg.imread(generation_plot)
                axes[1].imshow(gen_img)
                axes[1].axis("off")


            except FileNotFoundError:
                logger.error(f"File not found: {generation_plot}")
                axes[1].text(0.5, 0.5, "Generation plot not found", ha='center', va='center', fontsize=12, color='red')

            except Exception as e:
                logger.error(f"Error loading generation plot {generation_plot}: {e}")
                axes[1].text(0.5, 0.5, f"Error: {e}", ha='center', va='center', fontsize=12, color='red')

            # Adjust layout to remove excess space
            plt.tight_layout(pad=2.0)
            pdf.savefig(fig, dpi=300, bbox_inches='tight')
            plt.close(fig)
            logger.info(f"PDF saved to {pdf_filename}")
            return pdf_filename

    except Exception as e:
        logger.critical(f"Failed to generate PDF: {e}")
        raise



def merge_pdfs_plots(final_pdf, gen_plot_year, wind_plot_year, combined_30, output_filename):
    if not final_pdf or not gen_plot_year:
        logger.error("Skipping PDF merging due to missing files.")
        return None

    try:
        merger = PdfMerger()
        merger.append(final_pdf)
        merger.append(gen_plot_year)
        merger.append(wind_plot_year)
        merger.append(combined_30)
        merger.write(output_filename)
        merger.close()
        return output_filename
    except Exception as e:
        logger.error(f"Error merging PDFs: {e}")
        return None



def plot_plant_wise_generation_pdf(dataframe, plant_name, date_col="time"):
    try:
        logger.info(f"Starting plot_plant_wise_generation_pdf function for {plant_name}")

        output_pdf=f"{plant_name}_plant_wise_generation.pdf"

        generation_cols = dataframe.columns[1:]
        if date_col not in dataframe.columns or len(generation_cols) == 0:
            raise ValueError("Provided column names do not exist in the dataframe.")

        df = dataframe.copy()
        df[date_col] = pd.to_datetime(df[date_col], errors='coerce')
        df = df.dropna(subset=[date_col])
        df[date_col] = df[date_col].dt.tz_localize(None)

        num_plots = len(generation_cols)

        with PdfPages(output_pdf) as pdf:
            if num_plots == 1:
                fig, ax = plt.subplots(figsize=(12, 6))
                axes = [ax]
            else:
                num_rows = math.ceil(num_plots / 2)
                fig, axes = plt.subplots(nrows=num_rows, ncols=2, figsize=(16, 6 * num_rows + 1))
                axes = axes.flatten()

            fig.suptitle("GENERATION OVER LAST 12 MONTHS", fontsize=20, fontweight='bold', y=1.02, ha='center')

            for i, col in enumerate(generation_cols):
                df_monthly = df.resample('ME', on=date_col).sum()[col].reset_index()
                df_monthly['month_year'] = df_monthly[date_col].dt.strftime('%b %y')

                overall_avg = df_monthly[col].mean()

                bars = axes[i].bar(df_monthly['month_year'], df_monthly[col], color='cornflowerblue', alpha=0.7, label='Monthly Generation')
                axes[i].axhline(y=overall_avg, color='red', linestyle='--', label=f'Avg: {overall_avg:.2f} kWh')

                # Add value labels on bars
                for bar in bars:
                    height = bar.get_height()
                    if height > 0:
                        if height < overall_avg * 0.5:  # Small bars - label above
                            plt.text(bar.get_x() + bar.get_width() / 2, height + (overall_avg * 0.05), f"{height:.2f}",
                                    ha='center', va='bottom', fontsize=8, color='black')
                        else:  # Larger bars - label inside
                            plt.text(bar.get_x() + bar.get_width() / 2, height - (height * 0.1), f"{height:.2f}",
                                    ha='center', va='top', fontsize=8, color='black')

                axes[i].set_title("Turbine Generation")
                axes[i].set_xlabel("Month-Year")
                axes[i].set_ylabel("Total Energy Generated (kWh)")
                axes[i].tick_params(axis='x', rotation=45)
                axes[i].legend()
                axes[i].grid(axis='y', linestyle='--', alpha=0.7)

            # Hide any unused axes
            if num_plots > 1:
                for j in range(i + 1, len(axes)):
                    fig.delaxes(axes[j])

            plt.tight_layout(rect=[0, 0, 1, 0.96])
            pdf.savefig(fig, dpi=300, bbox_inches='tight')
            plt.close(fig)

        logger.info(f"Combined plant-wise generation plots saved to {output_pdf}")
        return output_pdf

    except Exception as e:
        logger.error(f"Error in plot_plant_wise_generation_pdf: {e}")
        raise



def plot_wind_speed_last_12_months(dataframe, plant_name, date_col="time"):
    try:

        output_pdf=f"{plant_name}_ wind_speed_last_12_months.pdf"
        logger.info(f"Starting plot_wind_speed_last_12_months function for {plant_name}")

        wind_cols = dataframe.columns[1:]
        if date_col not in dataframe.columns or len(wind_cols) == 0:
            raise ValueError("Provided column names do not exist in the dataframe.")

        df = dataframe.copy()
        df[date_col] = pd.to_datetime(df[date_col], errors='coerce')
        df = df.dropna(subset=[date_col])
        df[date_col] = df[date_col].dt.tz_localize(None)

        last_12_months = df[date_col].max() - pd.DateOffset(months=12)
        df = df[df[date_col] >= last_12_months]

        if df.empty:
            raise ValueError("No data available for the last 12 months.")

        num_plots = len(wind_cols)

        with PdfPages(output_pdf) as pdf:
            if num_plots == 1:
                fig, ax = plt.subplots(figsize=(12, 6))
                axes = [ax]
            else:
                num_rows = math.ceil(num_plots / 2)
                fig, axes = plt.subplots(nrows=num_rows, ncols=2, figsize=(16, 6 * num_rows + 1))
                axes = axes.flatten()

            fig.suptitle("AVG WIND SPEED OVER LAST 12 MONTHS", fontsize=20, fontweight='bold', y=1.02, ha='center')

            for i, col in enumerate(wind_cols):
                df_monthly = df.resample('ME', on=date_col).mean()[col].reset_index()
                df_monthly['month_year'] = df_monthly[date_col].dt.strftime('%b %y')

                overall_avg = df_monthly[col].mean()

                axes[i].plot(df_monthly['month_year'], df_monthly[col], marker='o', linestyle='-', color='cornflowerblue', label=f'Location {i+1}')
                axes[i].axhline(y=overall_avg, color='red', linestyle='--', label=f'Avg: {overall_avg:.2f} m/s')

                for x, y in zip(df_monthly['month_year'], df_monthly[col]):
                    axes[i].text(x, y + 0.1, f"{y:.2f}", ha='center', fontsize=9, color='black', rotation=45)

                axes[i].set_title("Wind Speed Analysis")
                axes[i].set_xlabel("Month-Year")
                axes[i].set_ylabel("Wind Speed (m/s)")
                axes[i].tick_params(axis='x', rotation=45)
                axes[i].legend()
                axes[i].grid(axis='y', linestyle='--', alpha=0.7)

            if num_plots > 1:
                for j in range(i + 1, len(axes)):
                    fig.delaxes(axes[j])

            plt.tight_layout(rect=[0, 0, 1, 0.96])
            pdf.savefig(fig, dpi=300, bbox_inches='tight')
            plt.close(fig)

        logger.info(f"Combined wind speed plots saved to {output_pdf}")
        return output_pdf

    except Exception as e:
        logger.error(f"Error in plot_wind_speed_last_12_months: {e}")
        raise
