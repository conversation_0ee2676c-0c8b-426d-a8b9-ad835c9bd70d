#--------------------------
# USING LANGSMITH WITH PROPER TRACING
#--------------------------

import asyncio
import os
import sys
import uuid
from contextlib import AsyncExitStack
from dataclasses import dataclass
from typing import List, Optional, Any, Tuple
from datetime import datetime
from dotenv import load_dotenv

# --- MCP core (Python SDK) ---
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

# --- LangChain / LangGraph stack ---
# LLM Orchestrator
from mcp_bot.llm_orchestrator import get_llm
from langchain.schema import BaseMessage
from langchain.callbacks.base import BaseCallbackHandler

# langgraph / langchain agent builder
from langchain_mcp_adapters.tools import load_mcp_tools
from langgraph.prebuilt import create_react_agent

# Prompts
from mcp_bot.prompts import get_system_prompt

# Lang<PERSON><PERSON> tracing helpers
import langsmith as ls

# Local DB helpers (keep your actual implementations)
from DB.db_ops import insert_chat_history, fetch_chat_history, delete_chat_history

# Logging helper used by original code
from helper.logger_setup import setup_logger

# ----------------------------
# Logging setup
# ----------------------------
logger = setup_logger('client', 'client.log')

# ----------------------------
# Load environment & defaults
# ----------------------------
load_dotenv()

# ----------------------------
# Callback for tool logging (console friendly)
# ----------------------------
class ToolLoggingCallback(BaseCallbackHandler):
    """
    Minimal callback to print tool start/end. Safe to attach to LLM and tools.
    """

    def on_tool_start(self, serialized: dict, input_str: str, **kwargs) -> None:
        tool_name = serialized.get("name", "unknown_tool")
        print(f"\n🛠️ LLM called tool: {tool_name}")
        print(f"   🔹 Input: {input_str}")

    def on_tool_end(self, output: Any, **kwargs) -> None:
        print(f"   🔸 Output: {output}\n")

# ----------------------------
# MCP server descriptor
# ----------------------------
@dataclass
class MCPServerSpec:
    """Defines an MCP server for stdio connection."""
    path: str
    args: Optional[List[str]] = None

    @property
    def command(self) -> str:
        return "python" if self.path.endswith(".py") else "node"

    @property
    def full_args(self) -> List[str]:
        return [self.path] + (self.args or [])

# ----------------------------
# Main MCPMultiServerClient
# ----------------------------
class MCPMultiServerClient:
    """
    Connects to one or more MCP servers (stdio), loads tools, creates a React-style agent,
    and uses LangSmith tracing_context to keep traces scoped to a specified LangSmith project.
    """

    def __init__(
        self,
        servers: List[MCPServerSpec],
        thread_id: Optional[str] = None,
        langsmith_project: str = "MCP-Client-Tracing",
    ):
        self.servers = servers
        self.exit_stack = AsyncExitStack()
        self.sessions: List[ClientSession] = []
        self.tools = []
        self.app = None

        # LangSmith project name to attribute traces to for this client
        self.langsmith_project = langsmith_project

        # Setup thread_id
        self.thread_id = thread_id or str(uuid.uuid4())

        # Load last 7 chat history messages from DB
        try:
            self.thread_history = fetch_chat_history(self.thread_id, last_n=7) or []
        except Exception as e:
            logger.error(f"[INIT] Failed to fetch chat history: {e}")
            self.thread_history = []

    # Internal helper to connect to one MCP server
    async def _connect_one(self, spec: MCPServerSpec) -> ClientSession:
        try:
            params = StdioServerParameters(command=spec.command, args=spec.full_args)
            read, write = await self.exit_stack.enter_async_context(stdio_client(params))
            session = await self.exit_stack.enter_async_context(ClientSession(read, write))
            await session.initialize()
            logger.info(f"[CONNECT] Connected to MCP server: {spec.path}")
            return session
        except Exception as e:
            logger.error(f"[CONNECT] Failed to connect to MCP server {spec.path}: {e}")
            raise

    async def connect(self) -> None:
        """
        Connect to all MCP servers, load their tools, and create the React agent with tracing scoped
        to self.langsmith_project during agent creation to ensure initialization traces go to the correct project.
        """

        # Validate LangSmith environment presence and log friendly warnings
        api_key = os.getenv("LANGSMITH_API_KEY") or os.getenv("LANGCHAIN_API_KEY")
        tracing_flag = os.getenv("LANGCHAIN_TRACING_V2", "").lower()

        if not api_key:
            logger.warning("[TRACE] LANGSMITH_API_KEY (or LANGCHAIN_API_KEY) not found in environment. Tracing may be disabled.")
        if tracing_flag != "true":
            logger.warning("[TRACE] LANGCHAIN_TRACING_V2 != 'true'. Ensure tracing v2 is enabled if you want LangSmith traces.")

        try:
            # Connect to each MCP server
            for spec in self.servers:
                session = await self._connect_one(spec)
                self.sessions.append(session)

            loaded_tools = []
            for s in self.sessions:
                maybe = load_mcp_tools(s)
                ts = await maybe if asyncio.iscoroutine(maybe) else maybe
                if ts:
                    loaded_tools.extend(ts)
            if not loaded_tools:
                logger.error("[TOOLS] No MCP tools found in any session")
                raise RuntimeError("No MCP tools found")

            self.tools = loaded_tools

            # Use LangSmith tracing_context to scope agent creation traces to our project.
            with ls.tracing_context(project_name=self.langsmith_project, tags=["mcp-client", "agent-init"]):
                # Instantiate LLM
                provider = os.getenv("LLM_PROVIDER", "openai").lower()
                llm = get_llm(provider, callbacks=[ToolLoggingCallback()])

                # Attach callback to tools as well (useful for local console debugging)
                for tool in loaded_tools:
                    try:
                        tool.callbacks = [ToolLoggingCallback()]
                    except Exception:
                        pass

                # Build a friendly tool description block used in system prompt
                # tool_descriptions = []
                # avalible_tools = []
                # for t in loaded_tools:
                #     desc = getattr(t, "description", "") or "No description provided"
                #     avalible_tools.append(t.name)
                #     tool_descriptions.append(f"tool_name: {t.name} -> tool_description: {desc}")
                # print("Avalible tools: ", avalible_tools)
                # tools_block = "\n".join(tool_descriptions)
                now = datetime.now()

                
                prompt = get_system_prompt(self.thread_id, now, provider)

                # Create the agent
                self.app = create_react_agent(
                    model=llm,
                    tools=self.tools,
                    checkpointer=None,
                    prompt=prompt,
                )

                logger.info("[CONNECT] Agent created successfully (traced to project=%s)", self.langsmith_project)

        except Exception as e:
            logger.error(f"[CONNECT] Failed to connect and initialize agent: {e}")
            raise

    async def ainvoke(self, user_text: str) -> str:
        """
        Invoke the agent with user_text. The entire invocation (which produces a run)
        is scoped inside a tracing_context so the run belongs to self.langsmith_project.
        """
        if self.app is None:
            logger.error("[INVOKE] Client not connected, app is None")
            raise RuntimeError("Client not connected. Call connect() first.")

        # Prepare state and config for agent
        messages_for_state: List[Tuple[str, str]] = [
            (e.get("role", "assistant"), e.get("content", "")) for e in self.thread_history
        ]
        messages_for_state.append(("user", user_text))
        state = {"messages": messages_for_state}
        config = {"configurable": {"thread_id": self.thread_id}}

        # Scope invocation to the LangSmith project using tracing_context
        with ls.tracing_context(project_name=self.langsmith_project, tags=["mcp-client", "invoke"]):
            try:
                result = await self.app.ainvoke(state, config=config)
                logger.debug("[INVOKE] Raw result: %s", str(result))
            except Exception as e:
                logger.error(f"[INVOKE] Agent invocation failed: {e}")
                raise

        # Normalize the result to text
        content = "(no output)"
        if isinstance(result, dict):
            if "messages" in result and isinstance(result["messages"], list) and result["messages"]:
                last = result["messages"][-1]
                if isinstance(last, BaseMessage):
                    content = last.content
                elif isinstance(last, tuple) and len(last) >= 2:
                    content = last[1]
                elif isinstance(last, dict):
                    content = last.get("content") or last.get("text") or str(last)
                else:
                    content = str(last)
            elif "output" in result:
                content = str(result["output"])
            elif "text" in result:
                content = str(result["text"])
            else:
                content = str(result)
        elif isinstance(result, str):
            content = result
        else:
            content = str(result)

        # Persist history to DB
        now = datetime.now().astimezone().isoformat()
        new_entries = [
            {"thread_id": self.thread_id, "role": "user", "content": user_text, "timestamp": now},
            {"thread_id": self.thread_id, "role": "assistant", "content": content, "timestamp": now},
        ]
        try:
            insert_chat_history(new_entries)
            self.thread_history.extend(new_entries)
        except Exception as e:
            logger.error(f"[DB] Failed to insert chat history: {e}")
        return content

    async def reset_history(self) -> None:
        """Clear persisted chat history for this thread."""
        try:
            delete_chat_history(self.thread_id)
            self.thread_history = []
            logger.info("[HISTORY] Reset history for thread_id=%s", self.thread_id)
        except Exception as e:
            logger.error(f"[HISTORY] Failed to reset history: {e}")
            raise

    async def close(self) -> None:
        """Close all sessions and exit contexts gracefully."""
        try:
            await self.exit_stack.aclose()
            logger.info("[CLOSE] Closed MCPMultiServerClient resources")
        except Exception as e:
            logger.warning(f"[CLOSE] Error during close: {e}")

    def _format_timestamp(self, iso_ts: Optional[str]) -> str:
        if not iso_ts:
            return ""
        try:
            dt = datetime.fromisoformat(iso_ts)
            return dt.strftime("%Y-%m-%d %H:%M:%S %z").strip()
        except Exception as e:
            logger.error(f"[HISTORY] Failed to format timestamp: {e}")
            return iso_ts

    def format_history(self, last_n: Optional[int] = None) -> str:
        """Return a human readable text dump of last_n history entries."""
        n = last_n if last_n is not None else 7
        try:
            hist = fetch_chat_history(self.thread_id, last_n=n) or []
        except Exception as e:
            logger.error(f"[HISTORY] Failed to fetch chat history: {e}")
            hist = []
        lines = []
        for i, entry in enumerate(hist, start=1):
            role = entry.get("role", "assistant").upper()
            ts = self._format_timestamp(entry.get("timestamp", ""))
            header = f"{i:03d} | {role:9} | {ts}" if ts else f"{i:03d} | {role:9}"
            body = entry.get("content", "")
            lines.append(header)
            lines.append(body)
            lines.append("-" * 80)
        return "\n".join(lines) if lines else "(no history)"



