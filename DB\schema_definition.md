# Database Schema Definition

This document describes the database schema, including tables, columns, types, and descriptions for each field.  

---

## Table: `whatsapp_messages`

| Column Name      | Type         | Description                                      |
|------------------|--------------|--------------------------------------------------|
| id               | Integer (PK, autoincrement) | Unique identifier for the message |
| wa_id            | String(20)   | WhatsApp user ID                                 |
| message_id       | String(100)  | Unique message identifier                        |
| report_type      | String(50)   | Type of report associated with the message       |
| plant_short_name | String(100)  | Short name of the plant                          |
| plant_long_name  | String(100)  | Long name of the plant                           |
| date             | Date         | Date of the message/report                       |
| dgr_path         | String(500)  | Path to the DGR report file                      |

---

## Table: `dgr_both_db`

| Column Name                  | Type         | Description                                                      |
|------------------------------|--------------|------------------------------------------------------------------|
| id                           | Integer (PK) | Unique identifier                                                |
| date                         | Date         | Date of the report                                               |
| plant_short_name_solar       | String       | Short name of the solar plant                                    |
| plant_long_name_solar        | String       | Long name of the solar plant                                     |
| generation_solar             | Float        | Solar plant generation (daily)                                   |
| pr                           | Float        | Performance ratio (solar)                                        |
| poa                          | Float        | Plane of array irradiance (solar)                                |
| generation_solar_monthly     | Float        | Solar plant generation (monthly)                                 |
| pr_monthly                   | Float        | Performance ratio (solar, monthly)                               |
| poa_monthly                  | Float        | Plane of array irradiance (solar, monthly)                       |
| plant_short_name_wind        | String       | Short name of the wind plant                                     |
| plant_long_name_wind         | String       | Long name of the wind plant                                      |
| generation_wind              | Float        | Wind plant generation (daily)                                    |
| wind_speed                   | Float        | Wind speed (daily)                                               |
| generation_wind_monthly      | Float        | Wind plant generation (monthly)                                  |
| wind_speed_monthly           | Float        | Wind speed (monthly)                                             |
| edit_generation_solar        | Float        | Edited solar generation (daily)                                  |
| edit_pr                      | Float        | Edited performance ratio (solar)                                 |
| edit_poa                     | Float        | Edited plane of array irradiance (solar)                         |
| edit_generation_solar_monthly| Float        | Edited solar generation (monthly)                                |
| edit_pr_monthly              | Float        | Edited performance ratio (solar, monthly)                        |
| edit_poa_monthly             | Float        | Edited plane of array irradiance (solar, monthly)                |
| edit_generation_wind         | Float        | Edited wind generation (daily)                                   |
| edit_wind_speed              | Float        | Edited wind speed (daily)                                        |
| edit_generation_wind_monthly | Float        | Edited wind generation (monthly)                                 |
| edit_wind_speed_monthly      | Float        | Edited wind speed (monthly)                                      |
| csv_report_data              | Text         | CSV report data (raw or JSON)                                    |
| edit_csv_report_data         | Text         | Edited CSV report data                                           |
| approved                     | Boolean      | Whether the report is approved                                   |
| review                       | Boolean      | Whether the report is under review                               |
| action_performed             | Boolean      | Whether an action has been performed on the report               |
| status                       | Enum         | Status of the report (see below)                                 |
| regenerate                   | Boolean      | Whether the report is marked for regeneration                    |
| dgr_path                     | String(500)  | Path to the DGR report file                                      |
| comments                     | String       | Comments on the report                                           |
| dont_send                    | Boolean      | Whether the report should not be sent                            |
| edit_action                  | Boolean      | Whether an edit action was performed                             |
| save_action                  | Boolean      | Whether a save action was performed                              |
| saved_count                  | Integer      | Number of times the report was saved                             |

---

## Table: `dgr_solar_db` (SolarReport)

| Column Name           | Type         | Description                                      |
|-----------------------|--------------|--------------------------------------------------|
| id                    | Integer (PK) | Unique identifier                                |
| date                  | Date         | Date of the report                               |
| plant_short_name      | String       | Short name of the solar plant                    |
| plant_long_name       | String       | Long name of the solar plant                     |
| generation            | Float        | Solar plant generation (daily)                   |
| pr                    | Float        | Performance ratio (solar)                        |
| poa                   | Float        | Plane of array irradiance (solar)                |
| generation_monthly    | Float        | Solar plant generation (monthly)                 |
| pr_monthly            | Float        | Performance ratio (solar, monthly)               |
| poa_monthly           | Float        | Plane of array irradiance (solar, monthly)       |
| edit_generation       | Float        | Edited solar generation (daily)                  |
| edit_pr               | Float        | Edited performance ratio (solar)                 |
| edit_poa              | Float        | Edited plane of array irradiance (solar)         |
| edit_generation_monthly| Float       | Edited solar generation (monthly)                |
| edit_pr_monthly       | Float        | Edited performance ratio (solar, monthly)        |
| edit_poa_monthly      | Float        | Edited plane of array irradiance (solar, monthly)|
| approved              | Boolean      | Whether the report is approved                   |
| review                | Boolean      | Whether the report is under review               |
| action_performed      | Boolean      | Whether an action has been performed on the report|
| status                | Enum         | Status of the report (see below)                 |
| regenerate            | Boolean      | Whether the report is marked for regeneration    |
| dgr_path              | String(500)  | Path to the DGR report file                      |
| comments              | String       | Comments on the report                           |
| dont_send             | Boolean      | Whether the report should not be sent            |
| edit_action           | Boolean      | Whether an edit action was performed             |
| save_action           | Boolean      | Whether a save action was performed              |
| saved_count           | Integer      | Number of times the report was saved             |

---

## Table: `dgr_wind_db` (WindReport)

| Column Name           | Type         | Description                                      |
|-----------------------|--------------|--------------------------------------------------|
| id                    | Integer (PK) | Unique identifier                                |
| date                  | Date         | Date of the report                               |
| plant_short_name      | String       | Short name of the wind plant                     |
| plant_long_name       | String       | Long name of the wind plant                      |
| generation            | Float        | Wind plant generation (daily)                    |
| wind_speed            | Float        | Wind speed (daily)                               |
| generation_monthly    | Float        | Wind plant generation (monthly)                  |
| wind_speed_monthly    | Float        | Wind speed (monthly)                             |
| edit_generation       | Float        | Edited wind generation (daily)                   |
| edit_wind_speed       | Float        | Edited wind speed (daily)                        |
| edit_generation_monthly| Float       | Edited wind generation (monthly)                 |
| edit_wind_speed_monthly| Float       | Edited wind speed (monthly)                      |
| csv_report_data       | Text         | CSV report data (raw or JSON)                    |
| edit_csv_report_data  | Text         | Edited CSV report data                           |
| approved              | Boolean      | Whether the report is approved                   |
| review                | Boolean      | Whether the report is under review               |
| action_performed      | Boolean      | Whether an action has been performed on the report|
| status                | Enum         | Status of the report (see below)                 |
| regenerate            | Boolean      | Whether the report is marked for regeneration    |
| dgr_path              | String(500)  | Path to the DGR report file                      |
| comments              | String       | Comments on the report                           |
| dont_send             | Boolean      | Whether the report should not be sent            |
| edit_action           | Boolean      | Whether an edit action was performed             |
| save_action           | Boolean      | Whether a save action was performed              |
| saved_count           | Integer      | Number of times the report was saved             |

---

## Table: `report_status`

| Column Name      | Type         | Description                                      |
|------------------|--------------|--------------------------------------------------|
| id               | Integer (PK, autoincrement) | Unique identifier                 |
| message_id       | String(255), unique | Unique message identifier                 |
| recipient_id     | String(255)  | ID of the recipient                              |
| status           | String(50)   | Status of the report/message                     |
| status_updated_at| DateTime     | When the status was last updated                 |
| send_date        | DateTime     | When the report/message was sent                 |
| report_date      | Date         | Date of the report                               |
| plant_id         | String(100)  | Plant identifier                                 |
| client_name      | String(255)  | Name of the client                               |
| type             | String(50)   | Type of report/message                           |
| combined         | String(100)  | Combined field (purpose inferred from name)      |
| contact_person   | Text         | Contact person details                           |

---

## Table: `chat_history`

| Column Name      | Type         | Description                                      |
|------------------|--------------|--------------------------------------------------|
| id               | Integer (PK, autoincrement) | Unique identifier                 |
| thread_id        | String(100), indexed | Conversation thread ID                   |
| role             | String(20)   | Role in the conversation (user, assistant, etc.) |
| content          | Text         | Message content                                  |
| timestamp        | String(40)   | ISO timestamp                                    |

---

## Table: `audit_logs`

| Column Name      | Type         | Description                                      |
|------------------|--------------|--------------------------------------------------|
| id               | Integer (PK, autoincrement) | Unique identifier                 |
| user_id          | Integer, indexed | User ID (nullable)                           |
| username         | String(50)   | Username (nullable)                              |
| action_type      | String(50)   | Type of action                                   |
| target_type      | String(50)   | Type of target (nullable)                        |
| target_id        | Integer      | Target ID (nullable)                             |
| details          | Text         | Details of the action                            |
| timestamp        | DateTime     | When the action occurred                         |
| ip_address       | String(45)   | IP address (nullable)                            |

---

## Table: `solar_inverter_data`

| Column Name           | Type         | Description                                      |
|-----------------------|--------------|--------------------------------------------------|
| id                    | Integer (PK, autoincrement) | Unique identifier                 |
| date                  | Date         | Date of the record                               |
| plant_id              | String(50)   | Plant identifier                                 |
| plant_name            | String(255)  | Name of the plant                                |
| inverter_name         | String(100)  | Name of the inverter                             |
| generation            | Float(2)     | Inverter generation (daily)                      |
| pr                    | Float(2)     | Performance ratio                                |
| poa                   | Float(2)     | Plane of array irradiance                        |
| generation_monthly    | Float(2)     | Inverter generation (monthly)                    |
| pr_monthly            | Float(2)     | Performance ratio (monthly)                      |
| poa_monthly           | Float(2)     | Plane of array irradiance (monthly)              |
| edit_action           | String(100)  | Edit action description                          |
| reason_edit           | Text         | Reason for edit                                  |
| edit_generation       | Float(2)     | Edited inverter generation (daily)               |
| edit_pr               | Float(2)     | Edited performance ratio                         |
| edit_poa              | Float(2)     | Edited plane of array irradiance                 |
| edit_generation_monthly| Float(2)    | Edited inverter generation (monthly)             |
| edit_pr_monthly       | Float(2)     | Edited performance ratio (monthly)               |
| edit_poa_monthly      | Float(2)     | Edited plane of array irradiance (monthly)       |
| created_at            | DateTime     | Record creation timestamp                        |
| updated_at            | DateTime     | Record update timestamp                          |

---

## Table: `plant_alarms`

| Column Name      | Type         | Description                                      |
|------------------|--------------|--------------------------------------------------|
| id               | Integer (PK, autoincrement) | Unique identifier                 |
| alarm_date       | Date         | Date of the alarm                                |
| plant_name       | String(255)  | Name of the plant                                |
| plant_id         | String(100)  | Plant identifier                                 |
| alarm_name       | String(255)  | Name of the alarm                                |
| controller_name  | String(255)  | Name of the controller (nullable)                |
| message          | Text         | Alarm message (nullable)                         |
| severity         | String(50)   | Severity of the alarm (nullable)                 |
| state            | String(50)   | State of the alarm (nullable)                    |
| raised_time      | DateTime     | When the alarm was raised                        |
| resolved_time    | DateTime     | When the alarm was resolved (nullable)           |
| duration_minutes | Integer      | Duration in minutes (nullable)                   |

---

## Table: `wind_turbine_data`

| Column Name           | Type         | Description                                      |
|-----------------------|--------------|--------------------------------------------------|
| id                    | Integer (PK, autoincrement) | Unique identifier                 |
| date                  | Date         | Date of the record                               |
| plant_id              | String(50)   | Plant identifier                                 |
| plant_name            | String(255)  | Name of the plant                                |
| turbine_name          | String(100)  | Name of the turbine                              |
| generation            | Float(2)     | Turbine generation (daily)                       |
| avg_wind_speed        | Float(2)     | Average wind speed (daily)                       |
| generation_monthly    | Float(2)     | Turbine generation (monthly)                     |
| avg_wind_speed_monthly| Float(2)     | Average wind speed (monthly)                     |
| edit_action           | String(100)  | Edit action description                          |
| reason_edit           | Text         | Reason for edit                                  |
| edit_generation       | Float(2)     | Edited turbine generation (daily)                |
| edit_avg_wind_speed   | Float(2)     | Edited average wind speed (daily)                |
| edit_generation_monthly| Float(2)    | Edited turbine generation (monthly)              |
| edit_avg_wind_speed_monthly| Float(2)| Edited average wind speed (monthly)              |
| created_at            | DateTime     | Record creation timestamp                        |
| updated_at            | DateTime     | Record update timestamp                          |

---

## Table: `admin_users`

| Column Name      | Type         | Description                                      |
|------------------|--------------|--------------------------------------------------|
| id               | Integer (PK, autoincrement) | Unique identifier                 |
| username         | String(50), unique, not null | Admin username                   |
| password_hash    | String(255), not null | Password hash                           |
| email            | String(120), unique | Email address                             |
| created_at       | DateTime     | Account creation timestamp                       |

---

## Table: `users`

| Column Name      | Type         | Description                                      |
|------------------|--------------|--------------------------------------------------|
| id               | Integer (PK, autoincrement) | Unique identifier                 |
| username         | String(50), unique, not null | Username                         |
| password_hash    | String(255), not null | Password hash                           |
| email            | String(120), unique | Email address                             |
| created_at       | DateTime     | Account creation timestamp                       |

---

**Enum values for `status` fields:**  
- 'Sent', 'Pending', 'In Review', 'Regenerated', 'Not Sent', 'Sent Updated', 'Saved', 'Updated', "Don't update"

**Notes:**  
- (PK) denotes Primary Key.  
- `autoincrement` means the value is automatically incremented by the database.  
- `unique` means the value must be unique in the table.  
- `not null` means the value cannot be null.  
- Descriptions are inferred from field names and available comments. For more detailed business logic, refer to the application code or documentation.
