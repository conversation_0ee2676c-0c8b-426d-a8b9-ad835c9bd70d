"""
Enhanced Solar Plotting Module using Seaborn + Matplotlib Hybrid
Improved plotting functions with better aesthetics and production-ready performance
"""

import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend for production
import matplotlib.pyplot as plt
import matplotlib.image as mpimg
from matplotlib.backends.backend_pdf import PdfPages
import seaborn as sns
import pandas as pd
import os
from helper.logger_setup import setup_logger

logger = setup_logger('solar_plots_enhanced', 'solar_plots_enhanced.log')

# Enhanced color palettes
SOLAR_PALETTE = ['#FF6B35', '#F7931E', '#FFD23F', '#06FFA5', '#118AB2', '#073B4C']

def setup_enhanced_style():
    """Setup enhanced Seaborn + Matplotlib styling for solar plots"""
    sns.set_style("whitegrid")
    sns.set_context("notebook", font_scale=1.1)
    sns.set_palette(SOLAR_PALETTE)

    plt.rcParams.update({
        'figure.facecolor': 'white',
        'axes.facecolor': '#FAFAFA',
        'axes.edgecolor': '#CCCCCC',
        'axes.linewidth': 0.8,
        'axes.grid': True,
        'grid.color': '#E0E0E0',
        'grid.alpha': 0.3,
        'xtick.color': '#333333',
        'ytick.color': '#333333',
        'text.color': '#333333',
        'legend.framealpha': 0.9,
        'legend.shadow': True
    })


def plot_monthly_generation(dataframe, plant_name, date_col="time", title="Daily Generation Over Last 30 Days"):
    """
    Enhanced daily generation plot using Seaborn + Matplotlib hybrid
    """
    try:
        logger.info(f"Starting enhanced plot_monthly_generation for {plant_name}")
        setup_enhanced_style()

        energy_col = dataframe.columns[1]
        if date_col not in dataframe.columns or energy_col not in dataframe.columns:
            raise ValueError("Provided column names do not exist in the dataframe.")

        df = dataframe.copy()
        df[date_col] = pd.to_datetime(df[date_col])

        # Generate last 30 days range
        end_date = df[date_col].max()
        start_date = end_date - pd.Timedelta(days=29)
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')

        # Reindex to fill missing dates with 0
        df = df.set_index(date_col).reindex(date_range).fillna(0).reset_index()
        df.columns = ['date', energy_col]

        average_generation = df[energy_col].mean()

        # Create enhanced figure
        fig, ax = plt.subplots(figsize=(14, 8))

        # Format dates for better display
        df['date_formatted'] = df['date'].dt.strftime('%m-%d')

        # Use Seaborn barplot for better aesthetics
        bars = sns.barplot(data=df, x='date_formatted', y=energy_col, ax=ax,
                          color=SOLAR_PALETTE[0], alpha=0.8)

        # Enhanced average line
        ax.axhline(average_generation, color=SOLAR_PALETTE[4], linestyle='--',
                  linewidth=3, alpha=0.9,
                  label=f"Average: {average_generation:.2f} kWh")

        # Enhanced value labels
        for i, bar in enumerate(bars.patches):
            height = bar.get_height()
            if height > 0:
                ax.text(bar.get_x() + bar.get_width()/2., height * 0.5,
                       f'{height:.1f}', ha='center', va='center',
                       fontsize=8, color='white', fontweight='bold', rotation=90)

        # Enhanced styling
        ax.set_title(title, fontsize=16, fontweight='bold', pad=20, color='#2C3E50')
        ax.set_xlabel("Date", fontsize=12, fontweight='bold')
        ax.set_ylabel("Daily Generation (kWh)", fontsize=12, fontweight='bold')

        # Improve x-axis formatting
        ax.tick_params(axis='x', rotation=45, labelsize=10)
        # Show every 3rd label to avoid crowding
        for i, label in enumerate(ax.get_xticklabels()):
            if i % 3 != 0:
                label.set_visible(False)

        ax.legend(loc='upper right', framealpha=0.9, shadow=True)

        plt.tight_layout()

        # Save with high quality
        output_filename = "static/plots_solar/" + f"{plant_name}_daily_generation.png"
        plt.savefig(output_filename, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        logger.info(f"Enhanced daily generation plot saved to {output_filename}")
        return output_filename

    except Exception as e:
        logger.error(f"Error in enhanced plot_monthly_generation: {e}")
        raise


def plot_yearly_generation(dataframe, plant_name, date_col="time", title="Last 12 Months Energy Generation"):
    """
    Enhanced yearly generation plot using Seaborn + Matplotlib hybrid
    """
    try:
        logger.info(f"Starting enhanced plot_yearly_generation for {plant_name}")
        setup_enhanced_style()

        energy_col = dataframe.columns[1]
        if date_col not in dataframe.columns or energy_col not in dataframe.columns:
            raise ValueError("Provided column names do not exist in the dataframe.")

        df = dataframe.copy()
        df[date_col] = pd.to_datetime(df[date_col]).dt.tz_localize(None)
        df["month_year"] = df[date_col].dt.strftime('%b %y')
        df["year_month"] = df[date_col].dt.to_period("M")
        df_monthly = df.groupby(["year_month", "month_year"])[energy_col].sum().reset_index()
        df_monthly = df_monthly.sort_values("year_month")

        overall_avg = df_monthly[energy_col].mean()

        # Create enhanced figure
        fig, ax = plt.subplots(figsize=(14, 8))

        # Use Seaborn barplot with gradient colors
        bars = sns.barplot(data=df_monthly, x="month_year", y=energy_col, ax=ax,
                          palette=sns.color_palette("viridis", len(df_monthly)),
                          alpha=0.8, legend=False)

        # Enhanced average line
        ax.axhline(y=overall_avg, color=SOLAR_PALETTE[4], linestyle='--',
                  linewidth=3, alpha=0.9, label=f'Average: {overall_avg:.2f} kWh')

        # Enhanced value labels
        for i, bar in enumerate(bars.patches):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + overall_avg * 0.02,
                   f'{height:.0f}', ha='center', va='bottom',
                   fontsize=9, fontweight='bold', color='#2C3E50')

        # Enhanced styling
        ax.set_title(title, fontsize=16, fontweight='bold', pad=20, color='#2C3E50')
        ax.set_xlabel("Month-Year", fontsize=12, fontweight='bold')
        ax.set_ylabel("Monthly Generation (kWh)", fontsize=12, fontweight='bold')
        ax.tick_params(axis='x', rotation=45)
        ax.legend(loc='upper right', framealpha=0.9, shadow=True)

        plt.tight_layout()

        # Save with high quality
        output_filename = "static/plots_solar/" + f"{plant_name}_yearly_generation.png"
        plt.savefig(output_filename, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        logger.info(f"Enhanced yearly generation plot saved to {output_filename}")
        return output_filename

    except Exception as e:
        logger.error(f"Error in enhanced plot_yearly_generation: {e}")
        raise


def plot_pr_monthly(dataframe, plant_name, date_col="time", title="Performance Ratio Over Last 30 Days"):
    """
    Enhanced PR monthly plot using Seaborn + Matplotlib hybrid
    """
    try:
        logger.info(f"Starting enhanced plot_pr_monthly for {plant_name}")
        setup_enhanced_style()

        pr_col = dataframe.columns[1]
        if date_col not in dataframe.columns or pr_col not in dataframe.columns:
            raise ValueError("Provided column names do not exist in the dataframe.")

        df = dataframe.copy()
        df[date_col] = pd.to_datetime(df[date_col], errors='coerce')
        df = df.dropna(subset=[date_col, pr_col])
        df[date_col] = df[date_col].dt.tz_localize(None)

        # Define last 30 days range
        end_date = df[date_col].max()
        start_date = end_date - pd.Timedelta(days=29)

        # Create a complete date range for 30 days
        date_range = pd.date_range(start=start_date, end=end_date, freq='D')
        df = df.set_index(date_col).reindex(date_range).reset_index()
        df.columns = ['date', pr_col]

        average_pr = df[pr_col].mean()

        # Create enhanced figure
        fig, ax = plt.subplots(figsize=(14, 8))

        # Format dates for better display
        df['date_formatted'] = df['date'].dt.strftime('%m-%d')

        # Use Seaborn lineplot with enhanced styling
        sns.lineplot(data=df, x='date_formatted', y=pr_col, ax=ax,
                    color=SOLAR_PALETTE[2], linewidth=2.5,
                    marker='o', markersize=6, alpha=0.8)

        # Enhanced average line
        ax.axhline(average_pr, color=SOLAR_PALETTE[4], linestyle='--',
                  linewidth=3, alpha=0.9,
                  label=f"Average: {average_pr:.2f}%")

        # Enhanced value labels (every 3rd point to avoid crowding)
        for i, (date_fmt, pr_val) in enumerate(zip(df['date_formatted'], df[pr_col])):
            if not pd.isna(pr_val) and i % 3 == 0:
                ax.text(i, pr_val + 1, f"{pr_val:.1f}",
                       ha='center', va='bottom', fontsize=8,
                       color='#2C3E50', rotation=45)

        # Enhanced styling
        ax.set_title(title, fontsize=16, fontweight='bold', pad=20, color='#2C3E50')
        ax.set_xlabel("Date", fontsize=12, fontweight='bold')
        ax.set_ylabel("Performance Ratio (%)", fontsize=12, fontweight='bold')

        # Improve x-axis formatting
        ax.tick_params(axis='x', rotation=45, labelsize=10)
        # Show every 3rd label to avoid crowding
        for i, label in enumerate(ax.get_xticklabels()):
            if i % 3 != 0:
                label.set_visible(False)

        ax.legend(loc='upper right', framealpha=0.9, shadow=True)
        ax.set_ylim(40, 100)

        plt.tight_layout()

        # Save with high quality
        output_filename = "static/plots_solar/" + f"{plant_name}_daily_pr.png"
        plt.savefig(output_filename, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        logger.info(f"Enhanced PR plot saved to {output_filename}")
        return output_filename

    except Exception as e:
        logger.error(f"Error in enhanced plot_pr_monthly: {e}")
        raise


def generate_solar_plot(image_files, data_values, pdf_filename, custom_titles, customer_name, date):
    """
    Enhanced solar plot PDF generation using Seaborn + Matplotlib hybrid
    """
    logger.info("Starting enhanced solar plot PDF generation...")
    setup_enhanced_style()

    try:
        title_header = f"Daily Generation Report | Customer: {customer_name} | Date: {date}"
        max_title_length = 60
        if len(title_header) > max_title_length:
            title_header = f"Daily Generation Report\nCustomer: {customer_name} | Date: {date}"

        with PdfPages(pdf_filename) as pdf:
            # Improved figure size and spacing for better alignment
            fig, axes = plt.subplots(2, 2, figsize=(20, 16))
            fig.suptitle(title_header, fontsize=20, fontweight="bold", y=0.96, color='#2C3E50')

            for i, (image_file, title) in enumerate(zip(image_files, custom_titles)):
                row, col = divmod(i, 2)
                ax = axes[row, col]

                try:
                    if image_file and os.path.exists(image_file):
                        img = mpimg.imread(image_file)
                        ax.imshow(img, aspect='auto')  # Use auto aspect for better fitting
                        ax.set_title(title, fontsize=16, fontweight='bold', color='#2C3E50', pad=15)
                    else:
                        ax.text(0.5, 0.5, f"Plot Not Available\n{title}",
                               ha='center', va='center', fontsize=14,
                               color='red', fontweight='bold')
                        ax.set_title(title, fontsize=16, fontweight='bold', color='#2C3E50', pad=15)
                except Exception as e:
                    logger.error(f"Error loading image {image_file}: {e}")
                    ax.text(0.5, 0.5, f"Error Loading Plot\n{title}",
                           ha='center', va='center', fontsize=14,
                           color='red', fontweight='bold')
                    ax.set_title(title, fontsize=16, fontweight='bold', color='#2C3E50', pad=15)

                ax.axis('off')

            # Improved layout with better spacing and margins
            plt.subplots_adjust(left=0.05, right=0.95, top=0.90, bottom=0.05,
                              hspace=0.25, wspace=0.15)
            pdf.savefig(fig, dpi=300, bbox_inches='tight', facecolor='white',
                       pad_inches=0.2)
            plt.close(fig)

        logger.info(f"Enhanced solar plot PDF saved to {pdf_filename}")
        return pdf_filename

    except Exception as e:
        logger.error(f"Error in enhanced generate_solar_plot: {e}")
        raise
