
from flask import Blueprint, render_template, request, redirect, url_for, flash, session, jsonify
from DB.setup_db import session as db_session
from DB.models import User, <PERSON>tL<PERSON>, AdminUser
from werkzeug.security import generate_password_hash, check_password_hash
from sqlalchemy import desc
from functools import wraps

admin_bp = Blueprint('admin', __name__, template_folder='../templates')

def admin_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        if not session.get('admin_logged_in'):
            flash('Admin login required.', 'danger')
            return redirect(url_for('admin.admin_login'))
        return f(*args, **kwargs)
    return decorated

@admin_bp.route('/admin', methods=['GET', 'POST'])
def admin_login():
    if session.get('admin_logged_in'):
        return redirect(url_for('admin.admin_dashboard'))
    error = None
    if request.method == 'POST':
        email = request.form.get('email', '').strip().lower()
        password = request.form.get('password', '')
        db = db_session()
        try:
            admin = db.query(AdminUser).filter(AdminUser.email == email).first()
            if admin and check_password_hash(admin.password_hash, password):
                session['admin_logged_in'] = True
                session['admin_username'] = admin.username
                flash('Admin login successful!', 'success')
                return redirect(url_for('admin.admin_dashboard'))
            else:
                error = 'Invalid admin credentials.'
        finally:
            db_session.remove()
    return render_template('admin.html', admin_login=True, error=error)

@admin_bp.route('/admin/dashboard', methods=['GET'])
@admin_required
def admin_dashboard():
    db = db_session()
    try:
        users = db.query(User).all()
        # Fetch distinct usernames and action types from AuditLog for dropdowns
        log_usernames = [row[0] for row in db.query(AuditLog.username).distinct().order_by(AuditLog.username).all() if row[0]]
        log_action_types = [row[0] for row in db.query(AuditLog.action_type).distinct().order_by(AuditLog.action_type).all() if row[0]]
    finally:
        db_session.remove()
    return render_template(
        'admin.html',
        admin_login=False,
        users=users,
        log_usernames=log_usernames,
        log_action_types=log_action_types
    )

@admin_bp.route('/admin/add_user', methods=['POST'])
@admin_required
def admin_add_user():
    username = request.form.get('username', '').strip()
    email = request.form.get('email', '').strip().lower()
    password = request.form.get('password', '')
    if not username or not email or not password:
        flash('All fields are required.', 'danger')
        return redirect(url_for('admin.admin_dashboard'))
    db = db_session()
    try:
        if db.query(User).filter((User.username == username) | (User.email == email)).first():
            flash('Username or email already exists.', 'danger')
            return redirect(url_for('admin.admin_dashboard'))
        password_hash = generate_password_hash(password)
        user = User(username=username, email=email, password_hash=password_hash)
        db.add(user)
        db.commit()
        flash(f"User '{username}' added successfully.", 'success')
    except Exception as e:
        db.rollback()
        flash(f"Error adding user: {e}", 'danger')
    finally:
        db_session.remove()
    return redirect(url_for('admin.admin_dashboard'))

@admin_bp.route('/admin/audit_logs', methods=['GET'])
@admin_required
def admin_audit_logs():
    # Optional filters: user, action_type, date range
    user = request.args.get('user', '').strip()
    action_type = request.args.get('action_type', '').strip()
    start_date = request.args.get('start_date', '').strip()
    end_date = request.args.get('end_date', '').strip()
    page = int(request.args.get('page', 1))
    per_page = int(request.args.get('per_page', 20))

    db = db_session()
    try:
        query = db.query(AuditLog)
        if user:
            query = query.filter(AuditLog.username.ilike(f"%{user}%"))
        if action_type:
            query = query.filter(AuditLog.action_type == action_type)
        if start_date:
            query = query.filter(AuditLog.timestamp >= start_date)
        if end_date:
            query = query.filter(AuditLog.timestamp <= end_date)
        total = query.count()
        logs = query.order_by(desc(AuditLog.timestamp)).offset((page-1)*per_page).limit(per_page).all()
        logs_data = [log.to_dict() for log in logs]
        return jsonify({
            "logs": logs_data,
            "total": total,
            "page": page,
            "per_page": per_page
        })
    finally:
        db_session.remove()

@admin_bp.route('/admin/plant_data', methods=['GET'])
@admin_required
def admin_plant_data():
    from datetime import datetime
    import traceback
    from DB.models import WindReport, SolarReport, DgrBothDb
    from sqlalchemy import desc

    plant_type = request.args.get('type', '').lower()
    plant_id = request.args.get('id', '').strip()

    try:
        if not plant_type or not plant_id:
            return jsonify({"success": False, "error": "Missing type or id"}), 400

        db = db_session()
        data = None

        if plant_type == 'wind':
            record = db.query(WindReport).filter(WindReport.plant_short_name == plant_id).order_by(desc(WindReport.date)).first()
            if record:
                d = record.to_dict()
                data = {
                    "Plant Name": d.get("plant_long_name"),
                    "Date": d.get("date"),
                    "Generation (Today)": d.get("generation"),
                    "Wind Speed (Today)": d.get("wind_speed"),
                    "Monthly Generation": d.get("generation_monthly"),
                    "Monthly Wind Speed": d.get("wind_speed_monthly"),
                    "Status": d.get("status"),
                }
            else:
                data = None

        elif plant_type == 'solar':
            record = db.query(SolarReport).filter(SolarReport.plant_short_name == plant_id).order_by(desc(SolarReport.date)).first()
            if record:
                d = record.to_dict()
                data = {
                    "Plant Name": d.get("plant_long_name"),
                    "Date": d.get("date"),
                    "Generation (Today)": d.get("generation"),
                    "PR (Today)": d.get("pr"),
                    "POA (Today)": d.get("poa"),
                    "Monthly Generation": d.get("generation_monthly"),
                    "Monthly PR": d.get("pr_monthly"),
                    "Monthly POA": d.get("poa_monthly"),
                    "Status": d.get("status"),
                }
            else:
                data = None

        elif plant_type == 'both':
            record = db.query(DgrBothDb).filter(
                (DgrBothDb.plant_short_name_solar == plant_id) | (DgrBothDb.plant_short_name_wind == plant_id)
            ).order_by(desc(DgrBothDb.date)).first()
            if record:
                d = record.to_dict()
                data = {
                    "Solar Plant Name": d.get("plant_long_name_solar"),
                    "Wind Plant Name": d.get("plant_long_name_wind"),
                    "Date": d.get("date"),
                    "Solar Generation (Today)": d.get("generation_solar"),
                    "Solar PR (Today)": d.get("pr"),
                    "Solar POA (Today)": d.get("poa"),
                    "Solar Monthly Generation": d.get("generation_solar_monthly"),
                    "Solar Monthly PR": d.get("pr_monthly"),
                    "Solar Monthly POA": d.get("poa_monthly"),
                    "Wind Generation (Today)": d.get("generation_wind"),
                    "Wind Speed (Today)": d.get("wind_speed"),
                    "Wind Monthly Generation": d.get("generation_wind_monthly"),
                    "Wind Monthly Wind Speed": d.get("wind_speed_monthly"),
                    "Status": d.get("status"),
                }
            else:
                data = None
        else:
            return jsonify({"success": False, "error": "Invalid plant type"}), 400

        db_session.remove()

        if data:
            return jsonify({"success": True, "data": data})
        else:
            return jsonify({"success": False, "error": "No data found for this plant."}), 404

    except Exception as e:
        traceback.print_exc()
        return jsonify({"success": False, "error": str(e)}), 500

@admin_bp.route('/admin/delete_user', methods=['POST'])
@admin_required
def admin_delete_user():
    email = request.form.get('email', '').strip().lower()
    if not email:
        flash('Email is required to delete a user.', 'danger')
        return redirect(url_for('admin.admin_dashboard'))
    db = db_session()
    try:
        user = db.query(User).filter(User.email == email).first()
        if not user:
            flash(f"User with email '{email}' not found.", 'danger')
            return redirect(url_for('admin.admin_dashboard'))
        db.delete(user)
        db.commit()
        flash(f"User with email '{email}' deleted successfully.", 'success')
    except Exception as e:
        db.rollback()
        flash(f"Error deleting user: {e}", 'danger')
    finally:
        db_session.remove()
    return redirect(url_for('admin.admin_dashboard'))
