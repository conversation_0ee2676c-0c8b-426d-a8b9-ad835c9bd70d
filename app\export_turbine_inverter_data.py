# import os
# import pandas as pd
# from datetime import datetime
# from DB.setup_db import session
# from DB.models import WindTurbineData, SolarInverterData
# from helper.logger_setup import setup_logger

# logger = setup_logger(__name__, "export_turbine_inverter_data.log")

# # === Professional Metadata for Excel Export ===

# WIND_SHEET_HEADERS = [
#     "Date", "Plant ID", "Plant Name", "Turbine Name",
#     "Prescinto Generation", "Prescinto  AVG. Wind Speed",
#     "Prescinto Generation Monthly", "Prescinto  AVG. Wind Speed Monthly",
#     "Edit Action", "Reason",
#     "Edit Generation", "Edit AVG. Wind Speed",
#     "Edit Generation Monthly", "Edit AVG. Wind Speed Monthly"
# ]

# SOLAR_SHEET_HEADERS = [
#     "Date", "Plant ID", "Plant Name", "Inverter Name",
#     "Prescinto Generation", "Prescinto  PR", "Prescinto POA",
#     "Prescinto  Generation Monthly", "Prescinto  PR Monthly", "Prescinto  POA Monthly",
#     "Edit Action", "Reason",
#     "Edit Generation", "Edit PR", "Edit POA",
#     "Edit Generation Monthly", "Edit PR Monthly", "Edit POA Monthly"
# ]

# ALARMS_SHEET_HEADERS = [
#     "Date", "Plant Name", "Plant ID", "Alarm Name", "Controller Name",
#     "Message", "Severity", "State", "Raised Time", "Resolved Time"
# ]

# def export_turbine_and_inverter_data_excel(start_date, end_date, output_path=None):
#     """
#     Export wind_turbine_data and solar_inverter_data for the given date range to an Excel file.
#     Args:
#         start_date (str): 'YYYY-MM-DD'
#         end_date (str): 'YYYY-MM-DD'
#         output_path (str): Path to save the Excel file. If None, saves to 'exports/turbine_inverter_{start}_{end}.xlsx'
#     Returns:
#         str: Path to the saved Excel file.
#     """

#     logger.info(f"Starting export for date range {start_date} to {end_date}")

#     try:
#         # Parse dates
#         start = datetime.strptime(start_date, "%Y-%m-%d")
#         end = datetime.strptime(end_date, "%Y-%m-%d")
#         logger.info("Parsed start and end dates successfully.")

#         # Query wind turbine data
#         logger.info("Querying wind turbine data from database...")
#         wind_query = session.query(WindTurbineData).filter(
#             WindTurbineData.date >= start,
#             WindTurbineData.date <= end
#         )
#         wind_rows = wind_query.all()
#         logger.info(f"Fetched {len(wind_rows)} wind turbine records.")
#         # Use .to_dict() to get all fields
#         wind_df = pd.DataFrame([row.to_dict() for row in wind_rows])
#         # Restore user-friendly headers and order
#         if not wind_df.empty:
#             wind_df = wind_df.rename(columns={
#                 "date": "Date",
#                 "plant_id": "Plant ID",
#                 "plant_name": "Plant Name",
#                 "turbine_name": "Turbine Name",
#                 "generation": "Prescinto Generation",
#                 "avg_wind_speed": "Prescinto  AVG. Wind Speed",
#                 "generation_monthly": "Prescinto Generation Monthly",
#                 "avg_wind_speed_monthly": "Prescinto  AVG. Wind Speed Monthly",
#                 "edit_action": "Edit Action",
#                 "reason_edit": "Reason",
#                 "edit_generation": "Edit Generation",
#                 "edit_avg_wind_speed": "Edit AVG. Wind Speed",
#                 "edit_generation_monthly": "Edit Generation Monthly",
#                 "edit_avg_wind_speed_monthly": "Edit AVG. Wind Speed Monthly",
#             })
#             # Add any extra fields not in headers
#             extra_cols = [col for col in wind_df.columns if col not in WIND_SHEET_HEADERS]
#             wind_df = wind_df[WIND_SHEET_HEADERS + extra_cols]
#             # Remove unwanted columns
#             for col in ["id", "created_at", "updated_at"]:
#                 if col in wind_df.columns:
#                     wind_df = wind_df.drop(columns=[col])
#         else:
#             logger.warning("No wind turbine data found for the given date range.")

#         # Query solar inverter data
#         logger.info("Querying solar inverter data from database...")
#         solar_query = session.query(SolarInverterData).filter(
#             SolarInverterData.date >= start,
#             SolarInverterData.date <= end
#         )
#         solar_rows = solar_query.all()
#         logger.info(f"Fetched {len(solar_rows)} solar inverter records.")
#         # Use .to_dict() to get all fields
#         solar_df = pd.DataFrame([row.to_dict() for row in solar_rows])
#         # Restore user-friendly headers and order
#         if not solar_df.empty:
#             solar_df = solar_df.rename(columns={
#                 "date": "Date",
#                 "plant_id": "Plant ID",
#                 "plant_name": "Plant Name",
#                 "inverter_name": "Inverter Name",
#                 "generation": "Prescinto Generation",
#                 "pr": "Prescinto  PR",
#                 "poa": "Prescinto POA",
#                 "generation_monthly": "Prescinto  Generation Monthly",
#                 "pr_monthly": "Prescinto  PR Monthly",
#                 "poa_monthly": "Prescinto  POA Monthly",
#                 "edit_action": "Edit Action",
#                 "reason_edit": "Reason",
#                 "edit_generation": "Edit Generation",
#                 "edit_pr": "Edit PR",
#                 "edit_poa": "Edit POA",
#                 "edit_generation_monthly": "Edit Generation Monthly",
#                 "edit_pr_monthly": "Edit PR Monthly",
#                 "edit_poa_monthly": "Edit POA Monthly",
#             })
#             # Add any extra fields not in headers
#             extra_cols = [col for col in solar_df.columns if col not in SOLAR_SHEET_HEADERS]
#             solar_df = solar_df[SOLAR_SHEET_HEADERS + extra_cols]
#             # Remove unwanted columns
#             for col in ["id", "created_at", "updated_at"]:
#                 if col in solar_df.columns:
#                     solar_df = solar_df.drop(columns=[col])
#         else:
#             logger.warning("No solar inverter data found for the given date range.")

#         # Output path
#         if output_path is None:
#             # Always create exports directory at project root
#             project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
#             exports_dir = os.path.join(project_root, "exports")
#             os.makedirs(exports_dir, exist_ok=True)
#             output_path = os.path.join(exports_dir, f"turbine_inverter_{start_date}_to_{end_date}.xlsx")
#         logger.info(f"Output Excel file will be saved to: {output_path}")

#         # Write to Excel
#         logger.info("Writing data to Excel file...")
#         with pd.ExcelWriter(output_path, engine="xlsxwriter") as writer:
#             wind_df.to_excel(writer, index=False, sheet_name="wind")
#             solar_df.to_excel(writer, index=False, sheet_name="solar")
#             alarms_df = pd.DataFrame(columns=ALARMS_SHEET_HEADERS)
#             alarms_df.to_excel(writer, index=False, sheet_name="alarm")

#             # Access workbook and worksheets
#             workbook  = writer.book
#             wind_ws   = writer.sheets["wind"]
#             solar_ws  = writer.sheets["solar"]

#             # Define drop-down options
#             dropdown_options = ["Updated", "Don't update"]

#             # Find the column index of "Edit Action"
#             wind_edit_action_col = WIND_SHEET_HEADERS.index("Edit Action")
#             solar_edit_action_col = SOLAR_SHEET_HEADERS.index("Edit Action")

#             # Apply data validation to all rows in "Edit Action" column (row 2 onward, since row 1 = headers)
#             wind_ws.data_validation(
#                 1, wind_edit_action_col, len(wind_df), wind_edit_action_col,
#                 {"validate": "list", "source": dropdown_options, "input_message": "Select edit action"}
#             )

#             solar_ws.data_validation(
#                 1, solar_edit_action_col, len(solar_df), solar_edit_action_col,
#                 {"validate": "list", "source": dropdown_options, "input_message": "Select edit action"}
#             )
#         logger.info("Excel file written successfully.")

#         return output_path
#     except Exception as e:
#         logger.exception("Error occurred during export_turbine_and_inverter_data_excel")
#         raise
#     finally:
#         session.close()

# if __name__ == "__main__":
#     # Example usage
#     try:
#         path = export_turbine_and_inverter_data_excel("2025-09-01", "2025-09-30")
#         logger.info(f"Exported Excel file: {path}")
#     except Exception as e:
#         logger.error(f"Failed to export Excel file: {e}")








import os
import pandas as pd
from datetime import datetime
from sqlalchemy.orm import sessionmaker
from DB.setup_db import engine
from DB.models import WindTurbineData, SolarInverterData

# === Professional Metadata for Excel Export ===

WIND_SHEET_HEADERS = [
    "Date", "Plant ID", "Plant Name", "Turbine Name",
    "Prescinto Generation", "Prescinto  AVG. Wind Speed",
    "Prescinto Generation Monthly", "Prescinto  AVG. Wind Speed Monthly",
    "Edit Action", "Reason",
    "Edit Generation", "Edit AVG. Wind Speed",
    "Edit Generation Monthly", "Edit AVG. Wind Speed Monthly",
    "Comments"
]

SOLAR_SHEET_HEADERS = [
    "Date", "Plant ID", "Plant Name", "Inverter Name",
    "Prescinto Generation", "Prescinto  PR", "Prescinto POA",
    "Prescinto  Generation Monthly", "Prescinto  PR Monthly", "Prescinto  POA Monthly",
    "Edit Action", "Reason",
    "Edit Generation", "Edit PR", "Edit POA",
    "Edit Generation Monthly", "Edit PR Monthly", "Edit POA Monthly",
    "Comments"
]

ALARMS_SHEET_HEADERS = [
    "Date", "Plant Name", "Plant ID", "Alarm Name", "Controller Name",
    "Message", "Severity", "State", "Raised Time", "Resolved Time"
]


def export_turbine_and_inverter_data_excel(start_date, end_date, output_path=None):
    """
    Export wind_turbine_data and solar_inverter_data for the given date range to an Excel file.
    Args:
        start_date (str): 'YYYY-MM-DD'
        end_date (str): 'YYYY-MM-DD'
        output_path (str): Path to save the Excel file. If None, saves to 'exports/turbine_inverter_{start}_{end}.xlsx'
    Returns:
        str: Path to the saved Excel file.
    """
    Session = sessionmaker(bind=engine)
    session = Session()
    try:
        # Parse dates
        start = datetime.strptime(start_date, "%Y-%m-%d")
        end = datetime.strptime(end_date, "%Y-%m-%d")

        # Query wind turbine data
        wind_query = session.query(WindTurbineData).filter(
            WindTurbineData.date >= start,
            WindTurbineData.date <= end
        )
        wind_rows = wind_query.all()
        wind_df = pd.DataFrame([row.__dict__ for row in wind_rows])
        if not wind_df.empty:
            wind_df = wind_df.drop(columns=["_sa_instance_state"])
            # Map ORM fields to required headers (add empty columns if missing)
            wind_df = wind_df.rename(columns={
    "date": "Date",
    "plant_id": "Plant ID",
    "plant_name": "Plant Name",
    "turbine_name": "Turbine Name",   
    "generation": "Prescinto Generation",
    "avg_wind_speed": "Prescinto  AVG. Wind Speed",
    "generation_monthly": "Prescinto Generation Monthly",
    "avg_wind_speed_monthly": "Prescinto  AVG. Wind Speed Monthly",
    "edit_action": "Edit Action",
    "reason_edit": "Reason",
    "edit_generation": "Edit Generation",
    "edit_avg_wind_speed": "Edit AVG. Wind Speed",
    "edit_generation_monthly": "Edit Generation Monthly",
    "edit_avg_wind_speed_monthly": "Edit AVG. Wind Speed Monthly",
})

            # Ensure all required columns are present and in order
            for col in WIND_SHEET_HEADERS:
                if col not in wind_df.columns:
                    wind_df[col] = ""
            # Set all "Edit" columns to null/empty string
            for col in WIND_SHEET_HEADERS:
                if col.startswith("Edit "):
                    wind_df[col] = ""
            # Ensure Comments column is empty string
            if "Comments" in WIND_SHEET_HEADERS:
                wind_df["Comments"] = ""
            wind_df = wind_df[WIND_SHEET_HEADERS]

        # Query solar inverter data
        solar_query = session.query(SolarInverterData).filter(
            SolarInverterData.date >= start,
            SolarInverterData.date <= end
        )
        solar_rows = solar_query.all()
        solar_df = pd.DataFrame([row.__dict__ for row in solar_rows])
        if not solar_df.empty:
            solar_df = solar_df.drop(columns=["_sa_instance_state"])
            solar_df = solar_df.rename(columns={
    "date": "Date",
    "plant_id": "Plant ID",
    "plant_name": "Plant Name",
    "inverter_name": "Inverter Name",  
    "generation": "Prescinto Generation",
    "pr": "Prescinto  PR",
    "poa": "Prescinto POA",
    "generation_monthly": "Prescinto  Generation Monthly",
    "pr_monthly": "Prescinto  PR Monthly",
    "poa_monthly": "Prescinto  POA Monthly",
    "edit_action": "Edit Action",
    "reason_edit": "Reason",
    "edit_generation": "Edit Generation",
    "edit_pr": "Edit PR",
    "edit_poa": "Edit POA",
    "edit_generation_monthly": "Edit Generation Monthly",
    "edit_pr_monthly": "Edit PR Monthly",
    "edit_poa_monthly": "Edit POA Monthly",
})

            for col in SOLAR_SHEET_HEADERS:
                if col not in solar_df.columns:
                    solar_df[col] = ""
            for col in SOLAR_SHEET_HEADERS:
                if col.startswith("Edit "):
                    solar_df[col] = ""
            # Ensure Comments column is empty string
            if "Comments" in SOLAR_SHEET_HEADERS:
                solar_df["Comments"] = ""
            solar_df = solar_df[SOLAR_SHEET_HEADERS]

        # Output path
        if output_path is None:
            # Always create exports directory at project root
            project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            exports_dir = os.path.join(project_root, "exports")
            os.makedirs(exports_dir, exist_ok=True)
            output_path = os.path.join(exports_dir, f"turbine_inverter_{start_date}_to_{end_date}.xlsx")


        # Write to Excel
        with pd.ExcelWriter(output_path, engine="xlsxwriter") as writer:
            wind_df.to_excel(writer, index=False, sheet_name="wind")
            solar_df.to_excel(writer, index=False, sheet_name="solar")
            alarms_df = pd.DataFrame(columns=ALARMS_SHEET_HEADERS)
            alarms_df.to_excel(writer, index=False, sheet_name="alarm")

            # Access workbook and worksheets
            workbook  = writer.book
            wind_ws   = writer.sheets["wind"]
            solar_ws  = writer.sheets["solar"]

            # Define drop-down options
            dropdown_options = ["Updated", "Don't update"]

            # Find the column index of "Edit Action"
            wind_edit_action_col = WIND_SHEET_HEADERS.index("Edit Action")
            solar_edit_action_col = SOLAR_SHEET_HEADERS.index("Edit Action")

            # Apply data validation to all rows in "Edit Action" column (row 2 onward, since row 1 = headers)
            wind_ws.data_validation(
                1, wind_edit_action_col, len(wind_df), wind_edit_action_col,
                {"validate": "list", "source": dropdown_options, "input_message": "Select edit action"}
            )

            solar_ws.data_validation(
                1, solar_edit_action_col, len(solar_df), solar_edit_action_col,
                {"validate": "list", "source": dropdown_options, "input_message": "Select edit action"}
            )


        return output_path
    finally:
        session.close()

if __name__ == "__main__":
    # Example usage
    path = export_turbine_and_inverter_data_excel("2025-09-01", "2025-09-30")
    print(f"Exported Excel file: {path}")
