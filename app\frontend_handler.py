import os
import json
import traceback
import csv as pycsv
from datetime import datetime, timedelta
import pandas as pd
from threading import Thread

from DB.setup_db import session as db_session

from flask import (
    jsonify, request, render_template,
    send_from_directory, flash, url_for, redirect, session, current_app as app
)

from functools import wraps # Import wraps

from app.process_tasks import (
    send_whatsapp_report_solar,
    send_whatsapp_report_wind,
    send_whatsapp_report_both
)

from DB.models import WindReport, SolarReport, DgrBothDb, ReportStatus, AuditLog, User
 
from sqlalchemy import func

from helper.logger_setup import setup_logger

from app.regenerate_reports import regenerate_task_both, regenerate_task_solar, regenerate_task_wind

from helper.utils import generate_solar_dgr_pdf, generate_dgr_wind_pdf, generate_combined_both_pdf

from werkzeug.security import check_password_hash
from io import BytesIO
from flask import send_file

# Logger Setup
logger = setup_logger('frontend_handler', 'frontend_handler.log')

from app.export_turbine_inverter_data import export_turbine_and_inverter_data_excel
from app.import_turbine_inverter_data import import_turbine_inverter_edits

def download_excel():
    """
    Download Excel file for Wind, Solar reports in the selected date range.
    Query params: start_date, end_date (YYYY-MM-DD)
    """
    start_date = request.args.get('start_date', '').strip()
    end_date = request.args.get('end_date', '').strip()
    if not start_date or not end_date:
        return jsonify({"error": "start_date and end_date are required"}), 400

    try:
        # Generate Excel file using the export function
        excel_path = export_turbine_and_inverter_data_excel(start_date, end_date)
        filename = os.path.basename(excel_path)
        # Audit log for download
        log_audit_action(
            action_type="download_excel",
            target_type="ExcelExport",
            target_id=None,
            details=f"Excel downloaded for range {start_date} to {end_date}, file: {filename}"
        )
        return send_file(
            excel_path,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )
    except Exception as e:
        import traceback
        traceback.print_exc()
        log_audit_action(
            action_type="download_excel_failed",
            target_type="ExcelExport",
            target_id=None,
            details=f"Failed to generate Excel for range {start_date} to {end_date}: {str(e)}"
        )
        return jsonify({"error": f"Failed to generate Excel: {str(e)}"}), 500

def log_audit_action(action_type, target_type=None, target_id=None, details=None):
    """
    Utility to log an audit action to the audit_logs table.
    """
    try:
        db = db_session()
        user_id = session.get('user_id')
        username = session.get('username')
        ip_address = request.remote_addr if request else None
        log = AuditLog(
            user_id=user_id,
            username=username,
            action_type=action_type,
            target_type=target_type,
            target_id=target_id,
            details=details,
            ip_address=ip_address
        )
        db.add(log)
        db.commit()
    except Exception as e:
        logger.error(f"Failed to log audit action: {e}")
    finally:
        db_session.remove()


# ---------------------------- ROUTES ---------------------------- #


# Login required decorator
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'logged_in' not in session:
            flash('Please log in to access this page.', 'warning')
            return redirect(url_for('login', next=request.url))
        return f(*args, **kwargs)
    return decorated_function


EMAIL_CREDENTIAL = os.getenv("APP_LOGIN_EMAIL")
PASSWORD_CREDENTIAL = os.getenv("APP_LOGIN_PASSWORD")



# def login():
#     logger.info("Login route accessed")
#     if request.method == 'POST':
#         email = request.form['email']
#         password = request.form['password']
#         logger.info(f"Login attempt for email: {email}")
#         if email == EMAIL_CREDENTIAL and password == PASSWORD_CREDENTIAL:
#             session['logged_in'] = True
#             flash('Login successful!', 'success')
#             logger.info(f"Login successful for email: {email}")
#             next_url = request.args.get('next')
#             return redirect(next_url or url_for('index'))
#         else:
#             flash('Invalid credentials. Please try again.', 'danger')
#             logger.warning(f"Invalid login attempt for email: {email}")
#     # If GET request or failed POST, show login page
#     return render_template('login.html')




def login():
    logger.info("Login route accessed")
    if request.method == 'POST':
        email = request.form['email'].strip().lower()
        password = request.form['password']
        logger.info(f"Login attempt for email: {email}")

        db = db_session()
        try:
            user = db.query(User).filter(
                (User.email == email) | (User.username == email)
            ).first()

            if user and check_password_hash(user.password_hash, password):
                # cache attributes before db_session.remove()
                user_id = user.id
                username = user.username

                session['logged_in'] = True
                session['user_id'] = user_id
                session['username'] = username

                log_audit_action(
                    action_type="login",
                    target_type="User",
                    target_id=user_id,
                    details=f"User {username} logged in."
                )
                flash('Login successful!', 'success')
                logger.info(f"Login successful for user: {username} ({email})")

                next_url = request.args.get('next')
                return redirect(next_url or url_for('index'))

            else:
                log_audit_action(
                    action_type="login_failed",
                    target_type="User",
                    target_id=user.id if user else None,
                    details=f"Failed login attempt for email/username: {email}"
                )
                flash('Invalid credentials. Please try again.', 'danger')
                logger.warning(f"Invalid login attempt for email/username: {email}")

        finally:
            db_session.remove()

    # If GET request or failed POST, show login page
    return render_template('login.html')



def logout():
    logger.info("User logged out")
    log_audit_action(
        action_type="logout",
        target_type="User",
        target_id=session.get('user_id'),
        details=f"User {session.get('username')} logged out."
    )
    session.pop('logged_in', None)
    session.pop('user_id', None)
    session.pop('username', None)
    flash('You have been logged out.', 'info')
    return redirect(url_for('login'))

@login_required # Protect the main page
def index():
    logger.info("Index route accessed")

    db = db_session()
    try:
        statuses = ['Sent', 'Sent Updated', 'Pending', 'In Review', 'Saved', 'Regenerated', 'Not Sent']

        # Count for WindReport
        wind_counts = dict(db.query(WindReport.status, func.count(WindReport.id)).group_by(WindReport.status).all())
        # Count for SolarReport
        solar_counts = dict(db.query(SolarReport.status, func.count(SolarReport.id)).group_by(SolarReport.status).all())
        # Count for DgrBothDb
        both_counts = dict(db.query(DgrBothDb.status, func.count(DgrBothDb.id)).group_by(DgrBothDb.status).all())

        # Prepare per-type status counts
        status_counts = {
            'wind': {status: wind_counts.get(status, 0) for status in statuses},
            'solar': {status: solar_counts.get(status, 0) for status in statuses},
            'both': {status: both_counts.get(status, 0) for status in statuses}
        }

        logger.info("Status counts prepared for index page")
        return render_template('index.html', status_counts=status_counts)
    finally:
        db_session.remove()

@login_required # Protect API route
def get_dgr_reports():
    logger.info("get_dgr_reports API called")
    start_date = request.args.get('start_date', '').strip()
    end_date = request.args.get('end_date', '').strip()

    db = db_session()  # ← Use renamed session
    try:
        query = db.query(WindReport)
        if start_date and end_date:
            query = query.filter(WindReport.date.between(start_date, end_date))
        reports = query.all()
        logger.info(f"Fetched {len(reports)} wind reports")
        return jsonify([report.to_dict() for report in reports])
    except Exception as e:
        logger.exception("Error in get_dgr_reports")
        return jsonify({"error": "internal server error"}), 500
    finally:
        db_session.remove() # Use scoped_session's remove method

@login_required # Protect API route
def get_solar_reports():
    logger.info("get_solar_reports API called")
    start_date = request.args.get("start_date")
    end_date = request.args.get("end_date")

    db = db_session()  # ← Use renamed session
    try:
        query = db.query(SolarReport).filter(
            SolarReport.date.between(start_date, end_date)
        )
        reports = query.all()
        logger.info(f"Fetched {len(reports)} solar reports")
        return jsonify([r.to_dict() for r in reports])
    except Exception as e:
        logger.exception("Error in get_solar_reports")
        return jsonify({"error": "internal server error"}), 500
    finally:
        db_session.remove() # Use scoped_session's remove method


@login_required # Protect API route
def get_both_reports():
    logger.info("get_both_reports API called")
    start_date = request.args.get('start_date', '').strip()
    end_date = request.args.get('end_date', '').strip()

    db = db_session()  # ← Use renamed session
    try:
        query = db.query(DgrBothDb)
        if start_date and end_date:
            query = query.filter(DgrBothDb.date.between(start_date, end_date))
        reports = query.all()
        logger.info(f"Fetched {len(reports)} both reports")
        return jsonify([report.to_dict() for report in reports])
    except Exception as e:
        logger.exception("Error in get_both_reports")
        return jsonify({"error": "internal server error"}), 500
    finally:
        db_session.remove() # Use scoped_session's remove method

@login_required # Protect API route
def _update_and_send(report, report_type, send_report_fn):
    try:
        if not report:
            return

        report.approved = True
        report.review = False
        # report.status = 'Sent'
        report.action_performed = True
        if report.edit_action == True:
            report.status = "Sent Updated"
        else:
            report.status = "Sent"

        plant = getattr(report, 'plant_short_name', '') or getattr(report, 'plant_short_name_solar', '')
        customer = getattr(report, 'plant_long_name', '') or getattr(report, 'plant_long_name_solar', '')
        report_date = report.date.strftime('%Y-%m-%d')

        def run():
            send_report_fn(plant, customer, report_date)

        Thread(target=run).start()

    except Exception as e:
        logger.error(f"Failed to trigger WhatsApp report for {report_type}: {e}")



@login_required # Protect API route
def update_dgr_wind():
    logger.info("update_dgr_wind API called")
    data = request.json
    ids = data['ids']
    approved = data.get('approved')
    review = data.get('review')
    dont_send = data.get('dont_send')
    comments = data.get('comments')
    save_action = data.get('save_action', False)

    updated_reports = []
    db = db_session() # Get session
    try:
        for id in ids:
            report = db.get(WindReport, id) # Use db session
            if report:
                db.refresh(report)  # Ensure latest edit_action from DB
                logger.debug(f"Processing wind report ID: {id}")
                if save_action:
                    report.status = "Saved"
                    report.save_action = True
                    report.saved_count = (report.saved_count or 0) + 1
                    if comments is not None:
                        report.comments = comments
                    logger.info(f"Wind report ID {id} saved with comments")
                    log_audit_action(
                        action_type="save",
                        target_type="WindReport",
                        target_id=report.id,
                        details=f"Saved report with comments: {comments}"
                    )
                    # Do not process review logic if save_action is True
                elif dont_send:
                    report.status = "Not Sent"
                    report.dont_send = True
                    if comments is not None:
                        report.comments = comments
                    logger.info(f"Wind report ID {id} marked as Not Sent")
                    log_audit_action(
                        action_type="dont_send",
                        target_type="WindReport",
                        target_id=report.id,
                        details=f"Marked as Not Sent. Comments: {comments}"
                    )
                else:
                    report.approved = approved
                    report.review = False
                    # Only set action_performed to True if approved
                    if approved:
                        # Use edit_action (set by edit_report) to determine status
                        if getattr(report, 'edit_action', False) == True:
                            report.status = "Sent Updated"
                        else:
                            report.status = "Sent"
                        _update_and_send(report, 'wind', send_whatsapp_report_wind)
                        report.action_performed = True # Mark as final action only on approval
                        logger.info(f"Wind report ID {id} approved and sent")
                        log_audit_action(
                            action_type="approve",
                            target_type="WindReport",
                            target_id=report.id,
                            details=f"Approved and sent report."
                        )
                    # If only review is True, don't set action_performed, but only if not save_action
                    elif review and not save_action:
                        report.status = 'In Review'
                        report.action_performed = False # Ensure it stays false if only reviewing
                        logger.info(f"Wind report ID {id} set to In Review")
                        log_audit_action(
                            action_type="review",
                            target_type="WindReport",
                            target_id=report.id,
                            details=f"Set report to In Review."
                        )
            updated_reports.append(report.to_dict())
            db.commit() # Use db session

        logger.info(f"Updated {len(updated_reports)} wind reports")
        return jsonify(updated_reports[0])  # Returning the updated record for frontend
    except Exception as e:
        db.rollback()
        logger.exception("Error updating wind report")
        return jsonify({"error": "Failed to update report"}), 500
    finally:
        db_session.remove() # Close session



@login_required # Protect API route
def update_solar():
    logger.info("update_solar API called")
    data = request.json
    ids = data['ids']
    approved = data.get('approved')
    review = data.get('review')
    dont_send = data.get('dont_send')
    comments = data.get('comments')
    save_action = data.get('save_action', False)

    updated_reports = []
    db = db_session() # Get session
    try:
        for id in ids:
            report = db.get(SolarReport, id) # Use db session
            if report:
                db.refresh(report)  # Ensure latest edit_action from DB
                logger.debug(f"Processing solar report ID: {id}")
                if save_action:
                    report.status = "Saved"
                    report.save_action = True
                    report.saved_count = (report.saved_count or 0) + 1
                    if comments is not None:
                        report.comments = comments
                    logger.info(f"Solar report ID {id} saved with comments")
                    log_audit_action(
                        action_type="save",
                        target_type="SolarReport",
                        target_id=report.id,
                        details=f"Saved report with comments: {comments}"
                    )
                    # Do not process review logic if save_action is True
                elif dont_send:
                    report.status = "Not Sent"
                    report.dont_send = True
                    if comments is not None:
                        report.comments = comments
                    logger.info(f"Solar report ID {id} marked as Not Sent")
                    log_audit_action(
                        action_type="dont_send",
                        target_type="SolarReport",
                        target_id=report.id,
                        details=f"Marked as Not Sent. Comments: {comments}"
                    )
                else:
                    report.approved = approved
                    report.review = False
                    # Only set action_performed to True if approved
                    if approved:
                        # Use edit_action (set by edit_report) to determine status
                        if getattr(report, 'edit_action', False) == True:
                            report.status = "Sent Updated"
                        else:
                            report.status = "Sent"
                        report.action_performed = True # Mark as final action only on approval
                        _update_and_send(report, 'solar', send_whatsapp_report_solar)
                        logger.info(f"Solar report ID {id} approved and sent")
                        log_audit_action(
                            action_type="approve",
                            target_type="SolarReport",
                            target_id=report.id,
                            details=f"Approved and sent report."
                        )
                    # If only review is True, don't set action_performed, but only if not save_action
                    elif review and not save_action:
                        report.status = 'In Review'
                        report.action_performed = False # Ensure it stays false if only reviewing
                        logger.info(f"Solar report ID {id} set to In Review")
                        log_audit_action(
                            action_type="review",
                            target_type="SolarReport",
                            target_id=report.id,
                            details=f"Set report to In Review."
                        )
            updated_reports.append(report.to_dict())
            db.commit() # Use db session

        logger.info(f"Updated {len(updated_reports)} solar reports")
        return jsonify(updated_reports[0])
    except Exception as e:
        db.rollback()
        logger.exception("Error updating solar report")
        return jsonify({"error": "Failed to update report"}), 500
    finally:
        db_session.remove() # Close session



@login_required # Protect API route
def update_both():
    data = request.json
    ids = data['ids']
    approved = data.get('approved')
    review = data.get('review')
    dont_send = data.get('dont_send')
    comments = data.get('comments')
    save_action = data.get('save_action', False)

    updated_reports = []
    db = db_session() # Get session
    try:
        for id in ids:
            report = db.get(DgrBothDb, id) # Use db session
            if report:
                db.refresh(report)  # Ensure latest edit_action from DB
                if save_action:
                    report.status = "Saved"
                    report.save_action = True
                    report.saved_count = (report.saved_count or 0) + 1
                    if comments is not None:
                        report.comments = comments
                    log_audit_action(
                        action_type="save",
                        target_type="DgrBothDb",
                        target_id=report.id,
                        details=f"Saved report with comments: {comments}"
                    )
                    # Do not process review logic if save_action is True
                elif dont_send:
                    report.status = "Not Sent"
                    report.dont_send = True
                    if comments is not None:
                        report.comments = comments
                    log_audit_action(
                        action_type="dont_send",
                        target_type="DgrBothDb",
                        target_id=report.id,
                        details=f"Marked as Not Sent. Comments: {comments}"
                    )
                else:
                    report.approved = approved
                    report.review = False
                    # Only set action_performed to True if approved
                    if approved:
                        # Use edit_action (set by edit_report) to determine status
                        if getattr(report, 'edit_action', False) == True:
                            report.status = "Sent Updated"
                        else:
                            report.status = "Sent"
                        report.action_performed = True # Mark as final action only on approval

                        def run():
                                send_whatsapp_report_both(
                                    report.plant_short_name_solar,
                                    report.plant_short_name_wind,
                                    report.plant_long_name_solar,
                                    report.date.strftime('%Y-%m-%d')
                                )

                        Thread(target=run).start()
                        log_audit_action(
                            action_type="approve",
                            target_type="DgrBothDb",
                            target_id=report.id,
                            details=f"Approved and sent report."
                        )

                    # If only review is True, don't set action_performed, but only if not save_action
                    elif review and not save_action:
                        report.status = 'In Review'
                        report.action_performed = False # Ensure it stays false if only reviewing
                        log_audit_action(
                            action_type="review",
                            target_type="DgrBothDb",
                            target_id=report.id,
                            details=f"Set report to In Review."
                        )
            updated_reports.append(report.to_dict())
        db.commit() # Use db session

        return jsonify(updated_reports[0]) # Returning the updated record for frontend
    except Exception as e:
        db.rollback()
        logger.error(f"❌ Error updating both report: {e}")
        return jsonify({"error": "Failed to update report"}), 500
    finally:
        db_session.remove() # Close session

@login_required # Protect API route
def get_status_counts():

    start_date = request.args.get('start_date', '').strip()
    end_date = request.args.get('end_date', '').strip()
    statuses = ['Sent', 'Sent Updated', 'Pending', 'In Review', 'Saved', 'Regenerated', 'Not Sent']

    db = db_session()
    try:
        # Wind
        wind_query = db.query(WindReport.status, func.count(WindReport.id))
        if start_date and end_date:
            wind_query = wind_query.filter(WindReport.date.between(start_date, end_date))
        wind_counts = dict(wind_query.group_by(WindReport.status).all())

        # Solar
        solar_query = db.query(SolarReport.status, func.count(SolarReport.id))
        if start_date and end_date:
            solar_query = solar_query.filter(SolarReport.date.between(start_date, end_date))
        solar_counts = dict(solar_query.group_by(SolarReport.status).all())

        # Both
        both_query = db.query(DgrBothDb.status, func.count(DgrBothDb.id))
        if start_date and end_date:
            both_query = both_query.filter(DgrBothDb.date.between(start_date, end_date))
        both_counts = dict(both_query.group_by(DgrBothDb.status).all())

        status_counts = {
            'wind': {status: wind_counts.get(status, 0) for status in statuses},
            'solar': {status: solar_counts.get(status, 0) for status in statuses},
            'both': {status: both_counts.get(status, 0) for status in statuses}
        }
        return jsonify(status_counts)
    finally:
        db_session.remove()

@login_required # Protect API route
def serve_pdf(report_type, report_id):
    logger.info(f"Serving PDF for type: {report_type}, ID: {report_id}")
    db = db_session()

    try:
        model_map = {
            'wind': WindReport,
            'solar': SolarReport,
            'both': DgrBothDb
        }

        model = model_map.get(report_type)
        if not model:
            log_audit_action(
                action_type="serve_pdf_failed",
                target_type="PDF",
                target_id=report_id,
                details=f"Invalid report type: {report_type}"
            )
            return jsonify({"error": "Invalid report type"}), 400

        report = db.get(model, report_id)
        if not report:
            log_audit_action(
                action_type="serve_pdf_failed",
                target_type="PDF",
                target_id=report_id,
                details=f"Report not found for type: {report_type}, id: {report_id}"
            )
            return jsonify({"error": "Report not found"}), 404

        # Choose PDF path based on edit_action
        if getattr(report, "edit_action", False):
            relative_pdf_path = report.dgr_path  # Edited PDF
        else:
            # If original is not preserved, this is the same as dgr_path
            relative_pdf_path = report.dgr_path  # Original PDF (or current if overwritten)
        if not relative_pdf_path:
            log_audit_action(
                action_type="serve_pdf_failed",
                target_type="PDF",
                target_id=report_id,
                details=f"PDF path not available for type: {report_type}, id: {report_id}"
            )
            return jsonify({"error": "PDF path not available"}), 404

        normalized_path = os.path.normpath(relative_pdf_path)
        pdf_dir = os.path.dirname(normalized_path)
        pdf_file = os.path.basename(normalized_path)

        # Update the root directory as needed
        # root = "D:\\Harikrishnan\\Wind_solar\\Whatsapp\\" # Local path
        # root = "//home/<USER>//dgr_updated//DGR-Generation//" #Deployment path
        root = "D:\\DGR-Generation\\"



        abs_path = os.path.join(root, pdf_dir, pdf_file)
        # abs_path = os.path.join(app.root_path, pdf_dir, pdf_file)

        if not os.path.exists(abs_path):
            logger.warning(f"PDF not found: {abs_path}")
            log_audit_action(
                action_type="serve_pdf_failed",
                target_type="PDF",
                target_id=report_id,
                details=f"PDF file not found at {abs_path} for type: {report_type}, id: {report_id}"
            )
            return jsonify({"error": "PDF file not found"}), 404

        log_audit_action(
            action_type="serve_pdf",
            target_type="PDF",
            target_id=report_id,
            details=f"Served PDF for type: {report_type}, id: {report_id}, file: {abs_path}"
        )
        return send_from_directory(os.path.dirname(abs_path), pdf_file, as_attachment=False)

    except Exception as e:
        logger.exception("Failed to serve PDF")
        log_audit_action(
            action_type="serve_pdf_failed",
            target_type="PDF",
            target_id=report_id,
            details=f"Exception serving PDF for type: {report_type}, id: {report_id}: {str(e)}"
        )
        return jsonify({"error": "Internal server error"}), 500

    finally:
        db.close()





def fetch_report_by_type_and_id(report_type, report_id):
    """
    Fetch a report by type and ID from the database.

    Args:
        report_type (str): One of "wind", "solar", or "both".
        report_id (int): The primary key ID of the report.

    Returns:
        dict: The report as a dictionary, or an error message.
    """
    db = db_session()
    try:
        model_map = {
            "wind": WindReport,
            "solar": SolarReport,
            "both": DgrBothDb
        }

        model = model_map.get(report_type)
        if not model:
            raise ValueError(f"Invalid report type: {report_type}")

        report = db.get(model, report_id)
        if report:
            return report.to_dict()
        else:
            return {"error": "Report not found"}
    finally:
        db.close()





@login_required
def api_regenerate():
    data = request.json
    report_id = data.get('id')
    report_type = data.get('type')
    plant_long_name_solar = None
    date = None


    # Fetch report data as dict for regeneration task input
    report_data = fetch_report_by_type_and_id(report_type, report_id)

    db = db_session()
    try:
        report = None

        if report_type == 'wind':
            report = db.get(WindReport, report_id)
            if report:
                plant_long_name_solar = report.plant_long_name
                date = str(report.date)

                # Use dict for regeneration task input
                wind_data = {
                    "date": date,
                    "plant_short_name": [report_data['plant_short_name']],
                    "plant_long_name": [report_data['plant_long_name']],
                    "original_id": report_id
                }
                # Mark the report as being regenerated BEFORE running the task
                report.status = 'Regenerated'
                report.regenerate = True
                report.action_performed = True
                db.commit()
                regenerate_task_wind(wind_data)

        elif report_type == 'solar':
            report = db.get(SolarReport, report_id)
            if report:
                plant_long_name_solar = report.plant_long_name
                date = str(report.date)

                solar_data = {
                    "date": date,
                    "plant_short_name": [report_data['plant_short_name']],
                    "plant_long_name": [report_data['plant_long_name']],
                    "original_id": report_id
                }
                report.status = 'Regenerated'
                report.regenerate = True
                report.action_performed = True
                db.commit()
                regenerate_task_solar(solar_data)

        elif report_type == 'both':
            report = db.get(DgrBothDb, report_id)
            if report:
                plant_long_name_solar = report.plant_long_name_solar
                date = report.date.strftime('%Y-%m-%d')

                both_data = {
                    "date": date,
                    "plant_short_name_wind": [report_data['plant_short_name_wind']],
                    "plant_short_name_solar": [report_data['plant_short_name_solar']],
                    "plant_long_name_wind": [report_data['plant_long_name_wind']],
                    "original_id": report_id
                }
                report.status = 'Regenerated'
                report.regenerate = True
                report.action_performed = True
                db.commit()
                regenerate_task_both(both_data)

        # Re-fetch the report to ensure it is bound to the session
        if report:
            if report_type == 'wind':
                report = db.get(WindReport, report_id)
            elif report_type == 'solar':
                report = db.get(SolarReport, report_id)
            elif report_type == 'both':
                report = db.get(DgrBothDb, report_id)


        # Audit log for regeneration
        log_audit_action(
            action_type="regenerate",
            target_type={
                "wind": "WindReport",
                "solar": "SolarReport",
                "both": "DgrBothDb"
            }.get(report_type, report_type),
            target_id=report_id,
            details=f"Regenerate triggered for report_id={report_id}, type={report_type}"
        )
        return jsonify({
            "success": True,
            "message": "Regenerate triggered",
            "id": report_id,
            "type": report_type,
            "plant_long_name_solar": plant_long_name_solar,
            "date": date,
            "row": report.to_dict() if report else None
        })

    except Exception as e:
        print("Error in /api/regenerate:", e)
        return jsonify({
            "success": False,
            "message": "Error in regenerate",
            "id": report_id,
            "type": report_type
        }), 500

    finally:
        db_session.remove()





def get_csv_path_by_db_id(db_id, json_path):
    """
    Fetches the CSV file path corresponding to a DB ID from the JSON mapping file.

    Args:
        db_id (int or str): The database ID for which to retrieve the CSV path.
        json_path (str): Path to the JSON file containing the mapping.

    Returns:
        str or None: The CSV path if found, else None.
    """
    try:
        with open(json_path, "r") as f:
            mapping = json.load(f)
        return mapping.get(str(db_id))  # ensure key is string
    except (FileNotFoundError, json.JSONDecodeError) as e:
        print(f"Error reading mapping file: {e}")
        return None







def edit_report(type, id):
    logger.info(f"edit_report called for type: {type}, id: {id}")
    import os
    import pandas as pd
    from flask import request, jsonify
    from helper.utils import (
        generate_solar_dgr_pdf,
        generate_combined_wind_pdf,
        generate_combined_both_pdf,
        get_capacity_from_csv
    )
    from DB.models import WindReport, SolarReport, DgrBothDb
    from DB.setup_db import session as db_session

    data = request.get_json()

    db = db_session()
    try:
        # Step 1: Get model class based on type
        model_map = {
            "solar": SolarReport,
            "wind": WindReport,
            "both": DgrBothDb
        }
        model = model_map.get(type)
        if not model:
            logger.error(f"Invalid report type: {type}")
            return jsonify({"error": "Invalid report type"}), 400

        report = db.get(model, id)
        if not report:
            logger.error(f"Report not found for type: {type}, id: {id}")
            return jsonify({"error": "Report not found"}), 404

        # Step 2: Determine if any edit_* values are actually different from the original values
        changes_made = False
        compare_fields = {
            "solar": [
                ("edit_generation", "generation"),
                ("edit_pr", "pr"),
                ("edit_poa", "poa"),
                ("edit_generation_monthly", "generation_monthly"),
                ("edit_pr_monthly", "pr_monthly"),
                ("edit_poa_monthly", "poa_monthly")
            ],
            "wind": [
                ("edit_generation", "generation"),
                ("edit_wind_speed", "wind_speed"),
                ("edit_generation_monthly", "generation_monthly"),
                ("edit_wind_speed_monthly", "wind_speed_monthly")
            ],
            "both": [
                ("edit_generation_solar", "generation_solar"),
                ("edit_pr", "pr"),
                ("edit_poa", "poa"),
                ("edit_generation_solar_monthly", "generation_solar_monthly"),
                ("edit_pr_monthly", "pr_monthly"),
                ("edit_poa_monthly", "poa_monthly"),
                ("edit_generation_wind", "generation_wind"),
                ("edit_wind_speed", "wind_speed"),
                ("edit_generation_wind_monthly", "generation_wind_monthly"),
                ("edit_wind_speed_monthly", "wind_speed_monthly")
            ]
        }

        changed_fields = []  # For audit log: list of (field, old, new)

        for edit_field, original_field in compare_fields.get(type, []):
            incoming_val = data.get(edit_field, None)
            original_val = getattr(report, original_field)

            def normalize(val):
                if val is None or val == "":
                    return None
                try:
                    return float(val)
                except (ValueError, TypeError):
                    return val

            norm_incoming = normalize(incoming_val)
            norm_original = normalize(original_val)

            if norm_incoming is None and norm_original is None:
                continue  # both empty/none, not a change
            elif isinstance(norm_incoming, float) and isinstance(norm_original, float):
                if round(norm_incoming, 4) != round(norm_original, 4):
                    changes_made = True
                    changed_fields.append((edit_field, original_val, incoming_val))
            else:
                if norm_incoming != norm_original:
                    changes_made = True
                    changed_fields.append((edit_field, original_val, incoming_val))

        # --- ADDITION: Check for changes in edit_csv_report_data for wind/both ---
        if type in ("wind", "both"):
            incoming_csv = data.get("edit_csv_report_data")
            current_csv = getattr(report, "edit_csv_report_data", None)
            if incoming_csv is not None and incoming_csv != current_csv:
                changes_made = True
                changed_fields.append(("edit_csv_report_data", "[previous csv data]", "[updated csv data]"))

        if changes_made:
            report.edit_action = True
            report.status = 'Sent Updated'
            logger.info(f"Edit action detected for report type: {type}, id: {id}")
        else:
            report.edit_action = False
            logger.info(f"No changes detected for report type: {type}, id: {id}")
            return jsonify({"status": "skipped", "message": "No changes detected."}), 200


        # Step 3: Update all edit_* fields from incoming data
        for key, value in data.items():
            if hasattr(report, key):
                try:
                    new_val = float(value) if value not in ("", None) else None
                except:
                    new_val = value
                setattr(report, key, new_val)

        # Step 4: Remove old PDF if it exists
        pdf_path = report.dgr_path
        if pdf_path and os.path.exists(pdf_path):
            try:
                os.remove(pdf_path)
                logger.info(f"Old PDF removed: {pdf_path}")
            except Exception as e:
                logger.warning(f"Could not remove old PDF: {e}")

        # Step 5: Regenerate the PDF
        date = str(getattr(report, "date"))


        # Save comments from edit modal (if present)
        if "edit_comments" in data:
            report.comments = data["edit_comments"]

        if type == "solar":
        
            generate_solar_dgr_pdf(
                date=date,
                customer_name=report.plant_long_name,
                poa=report.edit_poa or 0.0,
                pr=report.edit_pr or 0.0,
                daily_generation=report.edit_generations or 0.0,
                month_gen_value=report.edit_generation_monthly or 0.0,
                month_pr_value=report.edit_pr_monthly or 0.0,
                monthly_poa=report.edit_poa_monthly or 0.0,
                capacity=get_capacity_from_csv(report.plant_short_name),
                output_file=pdf_path,
                comment_text=report.comments if report.comments else None
            )
            logger.info(f"Solar PDF regenerated for report id: {id}")

        elif type == "wind":
            import os

            # Prefer edited CSV data if present, else original
            csv_data = report.edit_csv_report_data or report.csv_report_data
            if not csv_data:
                logger.error(f"No CSV data found for wind report id: {id}")
                return jsonify({"error": "No CSV data found for this report."}), 400

            # Ensure static/temp_csv directory exists
            static_csv_dir = os.path.join("static", "temp_csv")
            os.makedirs(static_csv_dir, exist_ok=True)

            # Write CSV data to a file in static/temp_csv
            import csv as pycsv
            import json

            csv_filename = f"report_{id}.csv"
            csv_path = os.path.join(static_csv_dir, csv_filename)
            try:
                # Try to detect and convert JSON-like string to CSV if needed
                csv_data_to_write = csv_data
                needs_conversion = False
                try:
                    # Try to parse as JSON array of dicts
                    parsed = json.loads(csv_data)
                    if isinstance(parsed, list) and all(isinstance(row, dict) for row in parsed):
                        needs_conversion = True
                except Exception:
                    needs_conversion = False

                if needs_conversion:
                    # Write as CSV with correct headers
                    with open(csv_path, "w", encoding="utf-8", newline='') as f:
                        writer = pycsv.DictWriter(f, fieldnames=["Loc No", "Avg Wind Speed", "Daily Generation (KWh)"])
                        writer.writeheader()
                        for row in parsed:
                            writer.writerow({
                                "Loc No": row.get("Loc No") or row.get("LocNo") or row.get("Loc_No") or "",
                                "Avg Wind Speed": row.get("Avg Wind Speed") or row.get("AvgWindSpeed") or row.get("Avg Wind Speed (m/s)") or "",
                                "Daily Generation (KWh)": row.get("Daily Generation (KWh)") or row.get("DailyGeneration") or row.get("Daily Generation (kWh)") or ""
                            })
                else:
                    with open(csv_path, "w", encoding="utf-8", newline='') as f:
                        f.write(csv_data)

                generate_combined_wind_pdf(
                    plant_name=report.plant_short_name,
                    start_date=date,
                    customer_name=report.plant_long_name,
                    project="WIND INTEGRUM",
                    avg_wind_speed=report.edit_wind_speed or 0.0,
                    total_generation=report.edit_generation or 0.0,
                    ma_percent=0.0,
                    monthly_wind=report.edit_wind_speed_monthly or 0.0,
                    monthly_generation=report.edit_generation_monthly or 0.0,
                    csv_filename=csv_path,
                    capacity=get_capacity_from_csv(report.plant_short_name),
                    output_file=pdf_path,
                    comment_text=report.comments if report.comments else None
                )
                logger.info(f"Wind PDF regenerated for report id: {id}")
            finally:
                try:
                    os.remove(csv_path)
                    logger.debug(f"Temp CSV file removed: {csv_path}")
                except Exception as e:
                    logger.warning(f"Could not remove temp CSV file: {e}")

        elif type == "both":
            import os

            # Prefer edited CSV data if present, else original
            csv_data = report.edit_csv_report_data or report.csv_report_data
            if not csv_data:
                logger.error(f"No CSV data found for both report id: {id}")
                return jsonify({"error": "No CSV data found for this report."}), 400

            # Ensure static/temp_csv directory exists
            static_csv_dir = os.path.join("static", "temp_csv")
            os.makedirs(static_csv_dir, exist_ok=True)

            # Write CSV data to a file in static/temp_csv
            import csv as pycsv
            import json

            csv_filename = f"report_{id}.csv"
            csv_path = os.path.join(static_csv_dir, csv_filename)
            wind_speed_df = None
            try:
                # Try to detect and convert JSON-like string to CSV if needed
                csv_data_to_write = csv_data
                needs_conversion = False
                parsed = None
                try:
                    parsed = json.loads(csv_data)
                    if isinstance(parsed, list) and all(isinstance(row, dict) for row in parsed):
                        needs_conversion = True
                except Exception:
                    needs_conversion = False

                if needs_conversion:
                    with open(csv_path, "w", encoding="utf-8", newline='') as f:
                        writer = pycsv.DictWriter(f, fieldnames=["Loc No", "Avg Wind Speed", "Daily Generation (KWh)"])
                        writer.writeheader()
                        for row in parsed:
                            writer.writerow({
                                "Loc No": row.get("Loc No") or row.get("LocNo") or row.get("Loc_No") or "",
                                "Avg Wind Speed": row.get("Avg Wind Speed") or row.get("AvgWindSpeed") or row.get("Avg Wind Speed (m/s)") or "",
                                "Daily Generation (KWh)": row.get("Daily Generation (KWh)") or row.get("DailyGeneration") or row.get("Daily Generation (kWh)") or ""
                            })
                    # Also convert to DataFrame for PDF
                    import pandas as pd
                    wind_speed_df = pd.DataFrame(parsed)
                else:
                    with open(csv_path, "w", encoding="utf-8", newline='') as f:
                        f.write(csv_data)
                    # Try to read as DataFrame if possible
                    import pandas as pd
                    try:
                        wind_speed_df = pd.read_csv(csv_path)
                    except Exception:
                        wind_speed_df = pd.DataFrame()

                generate_combined_both_pdf(
                    date=date,
                    customer_name=report.plant_long_name_solar,
                    project="INTEGRUM",
                    daily_poa=report.edit_poa or 0.0,
                    daily_pr=report.edit_pr or 0.0,
                    daily_gen_solar=report.edit_generation_solar or 0.0,
                    month_gen_solar=report.edit_generation_solar_monthly or 0.0,
                    month_pr_solar=report.edit_pr_monthly or 0.0,
                    monthly_poa_solar=report.edit_poa_monthly or 0.0,
                    daily_wind_speed=report.edit_wind_speed or 0.0,
                    daily_gen_wind=report.edit_generation_wind or 0.0,
                    monthly_wind=report.edit_wind_speed_monthly or 0.0,
                    monthly_gen_wind=report.edit_generation_wind_monthly or 0.0,
                    wind_capacity=get_capacity_from_csv(report.plant_short_name_wind),
                    solar_capacity=get_capacity_from_csv(report.plant_short_name_solar),
                    csv_filename=csv_path,
                    plant_name_solar=report.plant_short_name_solar,
                    plant_name_wind=report.plant_short_name_wind,
                    output_file=pdf_path,
                    comment_text=report.comments if report.comments else None
                )
                logger.info(f"Both PDF regenerated for report id: {id}")
            finally:
                try:
                    os.remove(csv_path)
                    logger.debug(f"Temp BOTH CSV file removed: {csv_path}")
                except Exception as e:
                    logger.warning(f"Could not remove BOTH temp CSV file: {e}")

        

        # Step 6: Save the updates
        db.commit()
        logger.info(f"Report updated and committed for type: {type}, id: {id}")

        # --- AUDIT LOG FOR EDIT REPORT ---
        if changes_made:
            # Prepare a readable string of changes
            if changed_fields:
                changes_str = "; ".join([
                    f"{field}: '{old}' -> '{new}'" for field, old, new in changed_fields
                ])
            else:
                changes_str = "Fields updated, but could not determine specific changes."

            log_audit_action(
                action_type="edit",
                target_type={
                    "solar": "SolarReport",
                    "wind": "WindReport",
                    "both": "DgrBothDb"
                }.get(type, type),
                target_id=id,
                details=f"Edited report. Changes: {changes_str}"
            )

        return jsonify({"status": "success", "updated": report.to_dict()}), 200

    except Exception as e:
        db.rollback()
        logger.exception("Error in edit_report")
        return jsonify({"error": "Failed to update report"}), 500
    finally:
        db_session.remove()



@login_required
def update_csv_data(type, report_id):
    """
    API endpoint to update edit_csv_report_data for a specific report.
    """
    try:
        data = request.get_json()
        csv_data = data.get('csv_data')
        
        if not csv_data:
            log_audit_action(
                action_type="update_csv_data_failed",
                target_type=type,
                target_id=report_id,
                details="CSV data is required"
            )
            return jsonify({"error": "CSV data is required"}), 400
        
        db = db_session()
        
        # Get the appropriate model
        model_map = {
            "wind": WindReport,
            "solar": SolarReport,
            "both": DgrBothDb
        }
        
        model = model_map.get(type)
        if not model:
            log_audit_action(
                action_type="update_csv_data_failed",
                target_type=type,
                target_id=report_id,
                details="Invalid report type"
            )
            return jsonify({"error": "Invalid report type"}), 400
        
        # Find the report
        report = db.get(model, report_id)
        if not report:
            log_audit_action(
                action_type="update_csv_data_failed",
                target_type=type,
                target_id=report_id,
                details="Report not found"
            )
            return jsonify({"error": "Report not found"}), 404
        
        # Update the edit_csv_report_data field
        report.edit_csv_report_data = csv_data
        db.commit()
        log_audit_action(
            action_type="update_csv_data",
            target_type=type,
            target_id=report_id,
            details="CSV data updated successfully"
        )
        return jsonify({
            "success": True,
            "message": "CSV data updated successfully"
        })
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating CSV data: {e}")
        log_audit_action(
            action_type="update_csv_data_failed",
            target_type=type,
            target_id=report_id,
            details=f"Exception: {str(e)}"
        )
        return jsonify({"error": "Failed to update CSV data"}), 500
    finally:
        db_session.remove()


@login_required
def get_plant_date_options():
    """
    API endpoint to fetch all unique plant names and all unique dates for a given type.
    Query param: type = wind | solar | both
    Returns: { plants: [...], dates: [...] }
    """
    try:
        report_type = request.args.get('type', 'both').lower()
        db = db_session()
        if report_type == 'wind':
            model = WindReport
            plant_field = model.plant_short_name
        elif report_type == 'solar':
            model = SolarReport
            plant_field = model.plant_short_name
        else:
            model = DgrBothDb
            plant_field = model.plant_short_name_solar

        # Get all unique plant names
        plants = [row[0] for row in db.query(plant_field).distinct().all() if row[0] is not None]
        # Get all unique dates (as ISO strings)
        dates = [row[0].isoformat() for row in db.query(model.date).distinct().order_by(model.date).all() if row[0] is not None]

        return jsonify({
            "success": True,
            "plants": plants,
            "dates": dates
        })
    except Exception as e:
        logger.error(f"Error in get_plant_date_options: {e}")
        return jsonify({"success": False, "error": str(e), "plants": [], "dates": []}), 500
    finally:
        db_session.remove()

@login_required
def get_report_status_unique_statuses():
    """
    API endpoint to fetch all unique status values from the ReportStatus table.
    Returns: { statuses: [...] }
    """
    try:
        db = db_session()
        statuses = [row[0] for row in db.query(ReportStatus.status).distinct().all() if row[0]]
        statuses = sorted(statuses)
        return jsonify({"success": True, "statuses": statuses})
    except Exception as e:
        logger.error(f"Error in get_report_status_unique_statuses: {e}")
        return jsonify({"success": False, "error": str(e), "statuses": []}), 500
    finally:
        db_session.remove()

@login_required
def get_report_status_options():
    """
    API endpoint to fetch all unique client names and plant IDs across all report types.
    Returns: { client_names: [...], plant_ids: [...] }
    """
    try:
        db = db_session()
        # WindReport
        wind_client_names = [row[0] for row in db.query(WindReport.plant_long_name).distinct().all() if row[0]]
        wind_plant_ids = [row[0] for row in db.query(WindReport.plant_short_name).distinct().all() if row[0]]
        # SolarReport
        solar_client_names = [row[0] for row in db.query(SolarReport.plant_long_name).distinct().all() if row[0]]
        solar_plant_ids = [row[0] for row in db.query(SolarReport.plant_short_name).distinct().all() if row[0]]
        # Both
        both_client_names = [row[0] for row in db.query(DgrBothDb.plant_long_name_solar).distinct().all() if row[0]]
        both_plant_ids = [row[0] for row in db.query(DgrBothDb.plant_short_name_solar).distinct().all() if row[0]]

        # Union and deduplicate
        client_names = sorted(set(wind_client_names + solar_client_names + both_client_names))
        plant_ids = sorted(set(wind_plant_ids + solar_plant_ids + both_plant_ids))

        return jsonify({
            "success": True,
            "client_names": client_names,
            "plant_ids": plant_ids
        })
    except Exception as e:
        logger.error(f"Error in get_report_status_options: {e}")
        return jsonify({"success": False, "error": str(e), "client_names": [], "plant_ids": []}), 500
    finally:
        db_session.remove()

@login_required
def dgr_data_upload():
    """
    Handle Excel upload for DGR data (Wind, Solar, Both sheets) using import_turbine_inverter_edits.
    Returns JSON for AJAX/fetch requests, and redirects with flash message for direct POST.
    """
    from werkzeug.utils import secure_filename
    import tempfile
    import os

    logger.info("Entered dgr_data_upload function (new import_turbine_inverter_edits version)")

    def is_ajax_request():
        return True if request.is_json or request.files else (
            request.headers.get("X-Requested-With") == "XMLHttpRequest"
            or "application/json" in request.headers.get("Accept", "")
        )

    if 'excel_file' not in request.files:
        logger.error("No file part in request.")
        log_audit_action(
            action_type="dgr_data_upload_failed",
            target_type="ExcelUpload",
            target_id=None,
            details="No file part in request."
        )
        if is_ajax_request():
            return jsonify({"success": False, "error": "No file part in request."}), 400
        else:
            flash("No file part in request.", "danger")
            return redirect(url_for('index'))

    file = request.files['excel_file']
    if file.filename == '':
        logger.error("No file selected.")
        log_audit_action(
            action_type="dgr_data_upload_failed",
            target_type="ExcelUpload",
            target_id=None,
            details="No file selected."
        )
        if is_ajax_request():
            return jsonify({"success": False, "error": "No file selected."}), 400
        else:
            flash("No file selected.", "danger")
            return redirect(url_for('index'))

    if not file.filename.lower().endswith('.xlsx'):
        logger.error("Only .xlsx files are supported.")
        log_audit_action(
            action_type="dgr_data_upload_failed",
            target_type="ExcelUpload",
            target_id=None,
            details="Only .xlsx files are supported."
        )
        if is_ajax_request():
            return jsonify({"success": False, "error": "Only .xlsx files are supported."}), 400
        else:
            flash("Only .xlsx files are supported.", "danger")
            return redirect(url_for('index'))

    # Save to a temp file
    with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as tmp:
        file.save(tmp.name)
        tmp_path = tmp.name

    logger.info(f"Excel file saved to temp path: {tmp_path}")

    try:
        # Call the new import function
        result = import_turbine_inverter_edits(tmp_path)

        logger.info(f"import_turbine_inverter_edits result: {result}")

        msg = (
            f"Upload successful. "
            f"Turbines updated: {result.get('turbine_updated', 0)}. "
            f"Inverters updated: {result.get('inverter_updated', 0)}. "
            f"Turbine+Inverter updated: {result.get('turbine_inverter_updated', 0)}. "
            f"Alarms added: {result.get('alarms_inserted', 0)}. "
            f"Plant-level updated: Wind: {result.get('plant_level_updated', {}).get('wind', 0)}, "
            f"Solar: {result.get('plant_level_updated', {}).get('solar', 0)}, "
            f"Both: {result.get('plant_level_updated', {}).get('both', 0)}. "
            f"Stovekraft paired: {result.get('stovekraft_paired', 0)}. "
            f"Jodhani/Balaji paired: {result.get('jodhani_balaji_paired', 0)}."
        )
        log_audit_action(
            action_type="dgr_data_upload",
            target_type="ExcelUpload",
            target_id=None,
            details=f"Excel uploaded: {file.filename}, {msg}"
        )
        if is_ajax_request():
            return jsonify({
                "success": True,
                "message": msg,
                "details": result
            })
        else:
            flash(msg, "success")
            return redirect(url_for('index'))
    except Exception as e:
        logger.error(f"Exception in dgr_data_upload (import_turbine_inverter_edits): {e}", exc_info=True)
        log_audit_action(
            action_type="dgr_data_upload_failed",
            target_type="ExcelUpload",
            target_id=None,
            details=f"Error processing file {file.filename if 'file' in locals() else ''}: {str(e)}"
        )
        if is_ajax_request():
            return jsonify({"success": False, "error": f"Error processing file: {str(e)}"}), 500
        else:
            flash(f"Error processing file: {str(e)}", "danger")
            return redirect(url_for('index'))
    finally:
        try:
            os.remove(tmp_path)
            logger.info(f"Temp file {tmp_path} removed.")
        except Exception as ex:
            logger.warning(f"Could not remove temp file {tmp_path}: {ex}")


@login_required
def get_whatsapp_status(type, report_id):
    """
    API endpoint to fetch WhatsApp send status for a specific report.
    Args:
        type (str): "wind", "solar", or "both"
        report_id (int): Report primary key
    Returns:
        JSON: {success, status, details}
    """
    try:
        db = db_session()
        # Map type to model and plant_id field
        model_map = {
            "wind": WindReport,
            "solar": SolarReport,
            "both": DgrBothDb
        }
        model = model_map.get(type)
        if not model:
            return jsonify({"success": False, "error": "Invalid report type"}), 400
        report = db.get(model, report_id)
        if not report:
            return jsonify({"success": False, "error": "Report not found"}), 404
        # Extract plant_id, client_name, date for lookup
        if type == "wind":
            plant_id = report.plant_short_name
            report_date = report.date
        elif type == "solar":
            plant_id = report.plant_short_name
            report_date = report.date
        else:  # both
            # plant_id = report.plant_short_name_wind
            plant_id = report.plant_short_name_solar
            report_date = report.date

        # Query ReportStatus for matching record(s)
        status_query = db.query(ReportStatus).filter(
            ReportStatus.plant_id == plant_id,
            ReportStatus.report_date == report_date,
        ).order_by(ReportStatus.status_updated_at.desc())

        status_records = status_query.all()

        if not status_records:
            return jsonify({"success": True, "status": "Not Found", "rows": []})

        rows = []
        for r in status_records:
            rows.append({
                "status": r.status,
                "recipient_id": r.recipient_id,
                "contact_person": r.contact_person,
                "message_id": r.message_id,
                "status_updated_at": r.status_updated_at.isoformat() if r.status_updated_at else None,
                "send_date": r.send_date.isoformat() if r.send_date else None
            })
        return jsonify({"success": True, "status": "OK", "rows": rows})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        db_session.remove()

@login_required
def get_report_status_data():
    """
    API endpoint to fetch report status data filtered by client_name, plant_id, status, and date (from ReportStatus table).
    Returns: [{report_date, plant, recipient_id, contact_person, status}, ...]
    """
    try:
        client_name = request.args.get('client_name', '').strip()
        plant_id = request.args.get('plant_id', '').strip()
        status = request.args.get('status', '').strip()
        start_date = request.args.get('start_date', '').strip()
        end_date = request.args.get('end_date', '').strip()

        db = db_session()
        query = db.query(ReportStatus)

        if client_name:
            query = query.filter(ReportStatus.client_name.ilike(f"%{client_name}%"))
        if plant_id:
            query = query.filter(ReportStatus.plant_id.ilike(plant_id))
        if status:
            query = query.filter(ReportStatus.status.ilike(status))
        if start_date:
            query = query.filter(ReportStatus.report_date >= start_date)
        if end_date:
            try:
                end_date_dt = datetime.strptime(end_date, "%Y-%m-%d")
                end_date_dt = end_date_dt.replace(hour=23, minute=59, second=59, microsecond=999999)
                query = query.filter(ReportStatus.report_date <= end_date_dt)
            except ValueError:
                query = query.filter(ReportStatus.report_date <= end_date)

        records = query.all()
        results = []

        for r in records:
            date_str = ""
            if r.report_date:
                try:
                    date_str = r.report_date.strftime("%Y-%m-%d")
                except Exception:
                    date_str = str(r.report_date).split("T")[0]

            if r.client_name and r.plant_id:
                plant_str = f"{r.client_name} ({r.plant_id})"
            elif r.client_name:
                plant_str = r.client_name
            elif r.plant_id:
                plant_str = r.plant_id
            else:
                plant_str = ""

            results.append({
                "report_date": date_str,
                "plant": plant_str,
                "recipient_id": r.recipient_id or "",
                "contact_person": r.contact_person or "",
                "status": r.status or ""
            })

        results.sort(key=lambda x: x["report_date"], reverse=True)
        return jsonify({"success": True, "data": results})

    except Exception as e:
        return jsonify({"success": False, "error": str(e), "data": []}), 500

    finally:
        db_session.remove()


@login_required
def get_report_status_counts():
    """
    API endpoint to fetch counts of each status from ReportStatus table,
    filtered by client_name, plant_id, status, start_date, and end_date.
    Returns: {status1: count1, status2: count2, ...}
    """
    try:
        client_name = request.args.get('client_name', '').strip()
        plant_id = request.args.get('plant_id', '').strip()
        status = request.args.get('status', '').strip()
        start_date = request.args.get('start_date', '').strip()
        end_date = request.args.get('end_date', '').strip()

        db = db_session()
        query = db.query(ReportStatus.status, func.count(ReportStatus.id))

        if client_name:
            query = query.filter(ReportStatus.client_name.ilike(client_name))
        if plant_id:
            query = query.filter(ReportStatus.plant_id.ilike(plant_id))
        if status:
            query = query.filter(ReportStatus.status.ilike(status))
        if start_date:
            query = query.filter(ReportStatus.report_date >= start_date)
        if end_date:
            try:
                end_date_dt = datetime.strptime(end_date, "%Y-%m-%d")

                query = query.filter(ReportStatus.report_date <= end_date_dt)
            except ValueError:
                query = query.filter(ReportStatus.report_date <= end_date)

        query = query.group_by(ReportStatus.status)
        counts = dict(query.all())

        all_statuses = db.query(ReportStatus.status).distinct().all()
        unique_statuses = sorted(set(s[0] for s in all_statuses if s[0]))

        # Capitalize status keys for frontend compatibility
        result = {s.capitalize(): counts.get(s, 0) for s in unique_statuses}

        return jsonify({"success": True, "counts": result})

    except Exception as e:
        return jsonify({"success": False, "error": str(e), "counts": {}}), 500

    finally:
        db_session.remove()


def get_data_analysis():
    """
    API endpoint for data analysis - returns all records with edit_action status.
    """
    try:
        report_type = request.args.get('type', 'both').lower()
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        db = db_session()
        analysis_data = []
        
        # Define metric mappings for each report type
        metric_mappings = {
            'wind': {
                'generation': ('generation', 'edit_generation'),
                'wind_speed': ('wind_speed', 'edit_wind_speed'),
                'generation_monthly': ('generation_monthly', 'edit_generation_monthly'),
                'wind_speed_monthly': ('wind_speed_monthly', 'edit_wind_speed_monthly')
            },
            'solar': {
                'generation': ('generation', 'edit_generation'),
                'pr': ('pr', 'edit_pr'),
                'poa': ('poa', 'edit_poa'),
                'generation_monthly': ('generation_monthly', 'edit_generation_monthly'),
                'pr_monthly': ('pr_monthly', 'edit_pr_monthly'),
                'poa_monthly': ('poa_monthly', 'edit_poa_monthly')
            },
            'both': {
                'generation_solar': ('generation_solar', 'edit_generation_solar'),
                'pr': ('pr', 'edit_pr'),
                'poa': ('poa', 'edit_poa'),
                'generation_solar_monthly': ('generation_solar_monthly', 'edit_generation_solar_monthly'),
                'pr_monthly': ('pr_monthly', 'edit_pr_monthly'),
                'poa_monthly': ('poa_monthly', 'edit_poa_monthly'),
                'generation_wind': ('generation_wind', 'edit_generation_wind'),
                'wind_speed': ('wind_speed', 'edit_wind_speed'),
                'generation_wind_monthly': ('generation_wind_monthly', 'edit_generation_wind_monthly'),
                'wind_speed_monthly': ('wind_speed_monthly', 'edit_wind_speed_monthly')
            }
        }
        
        # Get the appropriate model and query
        if report_type == 'wind':
            model = WindReport
            plant_name_field = 'plant_long_name'
        elif report_type == 'solar':
            model = SolarReport
            plant_name_field = 'plant_long_name'
        else:  # both
            model = DgrBothDb
            plant_name_field = 'plant_long_name_solar'  # Use solar plant name as primary
        
        # Build query (return all records, not just edited)
        query = db.query(model)
        if start_date:
            query = query.filter(model.date >= start_date)
        if end_date:
            query = query.filter(model.date <= end_date)
        
        # Execute query
        reports = query.all()
        
        # Process each report
        for report in reports:
            metrics = {}
            plant_name = getattr(report, plant_name_field)
            edit_action = getattr(report, "edit_action", False)
            status = getattr(report, "status", None)
            
            # Get metrics for this report type
            for metric_name, (original_field, edited_field) in metric_mappings[report_type].items():
                original_value = getattr(report, original_field, None)
                edited_value = getattr(report, edited_field, None)
                # Only include metrics where at least one value exists
                if (original_value is not None or edited_value is not None):
                    metrics[metric_name] = {
                        'original': float(original_value) if original_value is not None else None,
                        'edited': float(edited_value) if edited_value is not None else None
                    }
            
            analysis_data.append({
                'id': report.id,
                'date': report.date.isoformat(),
                'plant_name': plant_name,
                'metrics': metrics,
                'edit_action': bool(edit_action),
                'status': status
            })
        
        return jsonify({
            'success': True,
            'data': analysis_data,
            'total_records': len(analysis_data),
            'report_type': report_type
        })
        
    except Exception as e:
        logger.error(f"Error in get_data_analysis: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'data': []
        }), 500
    finally:
        db_session.remove()

@login_required
def get_generation_diff():
    print("get_generation_diff called")
    """
    API endpoint for daily original vs edited generation values for a given plant and date range.
    """
    try:
        # Fetch query parameters
        report_type = request.args.get('type', 'wind').lower()
        plant_name = request.args.get('plant_name', '').strip()
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        print(f"Request parameters - type: {report_type}, plant_name: {plant_name}, "
              f"start_date: {start_date}, end_date: {end_date}")

        db = db_session()
        results = []

        if report_type == 'wind':
            print("Processing wind reports")
            query = db.query(WindReport)
            if plant_name:
                query = query.filter(WindReport.plant_short_name == plant_name)
                print(f"Applied filter - plant_long_name: {plant_name}")
            if start_date:
                query = query.filter(WindReport.date >= start_date)
                print(f"Applied filter - start_date >= {start_date}")
            if end_date:
                query = query.filter(WindReport.date <= end_date)
                print(f"Applied filter - end_date <= {end_date}")

            wind_rows = query.order_by(WindReport.date).all()
            print(f"Fetched {len(wind_rows)} wind rows from DB")
            for r in wind_rows:
                print(f"Appending wind row - date: {r.date}, generation: {r.generation}, edit_generation: {r.edit_generation}")
                results.append({
                    "date": r.date.isoformat(),
                    "plant_name": r.plant_short_name,
                    "original_generation": r.generation,
                    "edited_generation": r.edit_generation,
                    "type": "wind"
                })

        elif report_type == 'solar':
            print("Processing solar reports")
            query = db.query(SolarReport)
            if plant_name:
                query = query.filter(SolarReport.plant_short_name == plant_name)
                print(f"Applied filter - plant_long_name: {plant_name}")
            if start_date:
                query = query.filter(SolarReport.date >= start_date)
                print(f"Applied filter - start_date >= {start_date}")
            if end_date:
                query = query.filter(SolarReport.date <= end_date)
                print(f"Applied filter - end_date <= {end_date}")

            solar_rows = query.order_by(SolarReport.date).all()
            print(f"Fetched {len(solar_rows)} solar rows from DB")
            for r in solar_rows:
                print(f"Appending solar row - date: {r.date}, generation: {r.generation}, edit_generation: {r.edit_generation}")
                results.append({
                    "date": r.date.isoformat(),
                    "plant_name": r.plant_short_name,
                    "original_generation": r.generation,
                    "edited_generation": r.edit_generation,
                    "type": "solar"
                })

        elif report_type == 'both':
            print("Processing both solar and wind reports")
            query = db.query(DgrBothDb)
            if plant_name:
                query = query.filter(DgrBothDb.plant_short_name_solar == plant_name)
                print(f"Applied filter - plant_long_name_solar: {plant_name}")
            if start_date:
                query = query.filter(DgrBothDb.date >= start_date)
                print(f"Applied filter - start_date >= {start_date}")
            if end_date:
                query = query.filter(DgrBothDb.date <= end_date)
                print(f"Applied filter - end_date <= {end_date}")

            both_rows = query.order_by(DgrBothDb.date).all()
            print(f"Fetched {len(both_rows)} both-type rows from DB")
            for r in both_rows:
                # Solar part
                print(f"Appending both-type solar row - date: {r.date}, generation: {r.generation_solar}, edit_generation: {r.edit_generation_solar}")
                results.append({
                    "date": r.date.isoformat(),
                    "plant_name": r.plant_short_name_solar,
                    "original_generation": r.generation_solar,
                    "edited_generation": r.edit_generation_solar,
                    "type": "both",
                    "subtype": "solar"
                })
                # Wind part
                print(f"Appending both-type wind row - date: {r.date}, generation: {r.generation_wind}, edit_generation: {r.edit_generation_wind}")
                results.append({
                    "date": r.date.isoformat(),
                    "plant_name": r.plant_short_name_wind,
                    "original_generation": r.generation_wind,
                    "edited_generation": r.edit_generation_wind,
                    "type": "both",
                    "subtype": "wind"
                })

        else:
            print(f"Invalid report type received: {report_type}")
            return jsonify({"success": False, "error": "Invalid type"}), 400

        print(f"Returning {len(results)} results")
        return jsonify({
            "success": True,
            "data": results
        })

    except Exception as e:
        print(f"Error in get_generation_diff: {e}")
        traceback.print_exc()
        return jsonify({"success": False, "error": str(e), "data": []}), 500

    finally:
        db_session.remove()
        print("Database session removed")



@login_required
def get_30day_trend():
    """
    API endpoint for 30-day trend plot.
    Returns daily counts of statuses ("Sent", "Sent Updated", "Pending") for the last 30 days from today.
    Optional query param: plant_name (to filter by plant)
    """
    try:
        db = db_session()
        today = datetime.now().date()
        start_date = today - timedelta(days=29)
        end_date = today

        plant_name = request.args.get('plant_name', None)

        # Helper to aggregate for a model
        def aggregate_model(model, plant_field, date_field, status_field):
            query = db.query(model)
            query = query.filter(getattr(model, date_field) >= start_date, getattr(model, date_field) <= end_date)
            if plant_name:
                query = query.filter(getattr(model, plant_field) == plant_name)
            return query.all()

        # Collect all reports for wind, solar, both
        wind_reports = aggregate_model(WindReport, 'plant_long_name', 'date', 'status')
        solar_reports = aggregate_model(SolarReport, 'plant_long_name', 'date', 'status')
        both_reports = aggregate_model(DgrBothDb, 'plant_long_name_solar', 'date', 'status')

        # Build date list
        date_list = [(start_date + timedelta(days=i)).isoformat() for i in range(30)]

        # Initialize counts
        trend = {date: {"Sent": 0, "Sent Updated": 0, "Pending": 0} for date in date_list}

        # Helper to increment counts
        def add_counts(reports, date_field, status_field):
            for r in reports:
                date_str = getattr(r, date_field).isoformat()
                status = getattr(r, status_field)
                if date_str in trend and status in trend[date_str]:
                    trend[date_str][status] += 1

        add_counts(wind_reports, 'date', 'status')
        add_counts(solar_reports, 'date', 'status')
        add_counts(both_reports, 'date', 'status')

        # Format for frontend: list of {date, sent, sent_updated, pending}
        trend_list = []
        for date in date_list:
            trend_list.append({
                "date": date,
                "Sent": trend[date]["Sent"],
                "Sent Updated": trend[date]["Sent Updated"],
                "Pending": trend[date]["Pending"]
            })

        return jsonify({
            "success": True,
            "trend": trend_list
        })
    except Exception as e:
        logger.error(f"Error in get_30day_trend: {e}")
        return jsonify({
            "success": False,
            "error": str(e),
            "trend": []
        }), 500
    finally:
        db_session.remove()
