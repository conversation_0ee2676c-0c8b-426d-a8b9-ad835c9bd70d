import os
from dotenv import load_dotenv
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

load_dotenv()

class Config:
    VERIFY_TOKEN = os.getenv('VERIFY_TOKEN')
    PUBLIC_URL = os.getenv('PUBLIC_URL')
    DEBUG = os.getenv('DEBUG', 'False') == 'True'
    CUSTOMER_DATA_CSV_PATH = os.getenv(
        'CUSTOMER_DATA_CSV_PATH',
        'static/customer_data - Sheet1.csv'
    )
    SERVER_PATH = "mcp_bot/server.py"
