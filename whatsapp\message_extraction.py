def extract_whatsapp_message_info(data):
    """
    Safely extracts relevant WhatsApp message info from the webhook payload.

    Args:
        data (dict): Webhook payload from WhatsApp.

    Returns:
        dict: A dictionary with extracted fields.
    """
    try:
        print(data)
        entry = data.get('entry', [{}])[0]
        change = entry.get('changes', [{}])[0].get('value', {})

        # Extract the contact and number
        contacts = change.get('contacts', [{}])
        incoming_num = contacts[0].get('wa_id') if contacts else None

        # Extract the message
        messages = change.get('messages', [{}])
        message = messages[0] if messages else {}

        # Message details
        incoming_message = None
        incoming_message = message.get('text', {}).get('body')
        message_id = message.get('id')
        button_text = message.get('button', {}).get('text')
        if incoming_message is None:
            message_id = change.get('messages', [{}])[0].get('context', {}).get('id')

        # Debug prints
        print(f"Incoming message: {incoming_message}")
        print(f"Incoming number: {incoming_num}")
        print(f"Button text: {button_text}")
        print(f"Message ID: {message_id}")

        return incoming_num, incoming_message, button_text, message_id
        

    except Exception as e:
        print(f"Error extracting WhatsApp message info: {e}")
        return {}










from DB.setup_db import session
from DB.models import ReportStatus
from datetime import datetime


def update_or_create_whatsapp_status(message_id, recipient_id, status, status_time, send_date=None):
    try:
        existing = session.query(ReportStatus).filter_by(message_id=message_id).first()
        if existing:
            if existing.status != status:
                existing.status = status
                existing.status_updated_at = status_time
        # else:
        #     # Lookup metadata from CSV
        #     metadata = get_metadata_by_contact_number(recipient_id)
        #     new_entry = ReportStatus(
        #         message_id=message_id,
        #         recipient_id=recipient_id,
        #         status=status,
        #         status_updated_at=status_time,
        #         send_date=send_date or datetime.now(),
        #         plant_id=metadata.get("plant_id", ""),
        #         client_name=metadata.get("client_name", ""),
        #         type=metadata.get("type", ""),
        #         combined=metadata.get("combined", ""),
        #         contact_person=metadata.get("contact_person", "")
        #     )
        #     session.add(new_entry)
        session.commit()
    except Exception as e:
        print(f"DB Error updating status for {message_id}: {e}")
        session.rollback()
    finally:
        session.close()
