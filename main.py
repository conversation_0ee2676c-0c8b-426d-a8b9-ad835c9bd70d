from app import create_app
from DB.setup_db import session
import threading
from app.schedule_task import run_scheduler
import os




app = create_app()


# Teardown context
@app.teardown_appcontext
def remove_session(*args, **kwargs):
    session.remove()







if __name__ == "__main__":
    scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
    scheduler_thread.start()
    app.run(debug=False, host='0.0.0.0', port=5000, use_reloader=False)

