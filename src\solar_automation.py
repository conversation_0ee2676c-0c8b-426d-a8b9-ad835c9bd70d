import os
from pathlib import Path
from typing import Dict, Any, Optional

import pandas as pd

from helper.utils import (
    generate_solar_dgr_pdf,
    get_dynamic_dates,
    fetch_data_total,
    get_capacity_from_csv,
    _safe_mean,
    _safe_sum,
)
from helper.logger_setup import setup_logger
from helper.storage_s3 import upload_file_s3
from DB.db_ops import insert_solar_data_db, insert_solar_inverter_data_db

# -------------------------------
# Constants
# -------------------------------
STATIC_REPORT_DIR = Path("static") / "solar_final_report"
S3_REPORT_PREFIX = "solar_reports/"

LOGGER = setup_logger("solar_automation", "solar_automation.log")


# -------------------------------
# Data Fetching
# -------------------------------
def fetch_all_solar_data(
    plant_name: str,
    start_date: str,
    current_month_start: str,
    last_30_days_start: str,
    current_year_start: str,
    yearly: str,
    condition_poa: Dict[str, str],
    condition_pr: Dict[str, str],
    condition_generation: Dict[str, str],
    condition_monthly_pr: Dict[str, str]
) -> Dict[str, pd.DataFrame]:
    """
    Fetch all solar data sequentially to reduce RAM usage.
    """
    LOGGER.info(f"[Data Fetch] Plant: {plant_name}")

    return {
        "poa_data": fetch_data_total(plant_name, ["Daily POA Energy"], "Plant", start_date, start_date, condition_poa),
        "pr_data": fetch_data_total(plant_name, ["PR"], "Plant", start_date, start_date, condition_pr),
        "daily_generation": fetch_data_total(plant_name, ["Daily Energy"], "Plant", start_date, start_date, condition_generation),
        "generation_monthly_value": fetch_data_total(plant_name, ["Daily Energy"], "Plant", current_month_start, start_date, condition_generation),
        "pr_data_monthly_value": fetch_data_total(plant_name, ["PR"], "Plant", current_month_start, start_date, condition_monthly_pr),
        "poa_data_monthly_value": fetch_data_total(plant_name, ["Daily POA Energy"], "Plant", current_month_start, start_date, condition_poa),



        "pr_data_inverter": fetch_data_total(plant_name, ["PR"], "Inverter", start_date, start_date, condition_pr),
        "daily_generation_inverter": fetch_data_total(plant_name, ["Daily Energy"], "Inverter", start_date, start_date, condition_generation),
        "pr_data_inverter_monthly": fetch_data_total(plant_name, ["PR"], "Inverter", current_month_start, start_date, condition_monthly_pr),
        "daily_generation_inverter_monthly": fetch_data_total(plant_name, ["Daily Energy"], "Inverter", current_month_start, start_date, condition_generation),
    }


# -------------------------------
# Main Report Generation
# -------------------------------
def generate_solar_automation_report(
    plant_name: str,
    start_date: str,
    customer_name: str,
    project: str
) -> Optional[Path]:
    """
    Generate and upload a daily solar automation report for a given plant.
    """
    LOGGER.info(f"[Start Report] Plant: {plant_name}, Date: {start_date}, Customer: {customer_name}")

    try:
        # Define fetch conditions
        condition_poa = {"Daily POA Energy": "last"}
        condition_daily_pr = {"PR": "last"}
        condition_monthly_pr = {"PR": "mean"}
        condition_generation = {"Daily Energy": "max"}

        # Get date ranges
        current_month_start, _, _, _, _, last_30_days_start, current_year_start, last_year_date = get_dynamic_dates(start_date)

        # Fetch all required data
        data = fetch_all_solar_data(
            plant_name, start_date, current_month_start, last_30_days_start,
            current_year_start, last_year_date, condition_poa, condition_daily_pr,
            condition_generation, condition_monthly_pr
        )

        # Extract and compute key metrics
        total_generation = _safe_sum(data["daily_generation"])
        month_gen_value = _safe_sum(data["generation_monthly_value"])
        month_pr_value = _safe_mean(data["pr_data_monthly_value"])
        daily_pr_percentage = _safe_mean(data["pr_data"])
        monthly_poa_value = _safe_mean(data["poa_data_monthly_value"])
        avg_poa = _safe_mean(data["poa_data"])

        capacity = get_capacity_from_csv(plant_name)

        # Prepare report path
        STATIC_REPORT_DIR.mkdir(parents=True, exist_ok=True)
        final_pdf_path = STATIC_REPORT_DIR / f"{plant_name}_DGR_{start_date}.jpg"

        # Generate PDF report
        summary_pdf = generate_solar_dgr_pdf(
            start_date, customer_name, avg_poa, daily_pr_percentage,
            total_generation, month_gen_value, month_pr_value,
            monthly_poa_value, capacity, final_pdf_path, comment_text=None
        )

        LOGGER.info(f"[Report Generated] {summary_pdf}")

        # Upload to S3
        s3_path = f"{S3_REPORT_PREFIX}{plant_name}_DGR_{start_date}.jpg"
        upload_file_s3(summary_pdf, s3_path)

        # Insert into DB
        record = {
            "date": start_date,
            "plant_short_name": plant_name,
            "plant_long_name": customer_name,
            "generation": round(total_generation, 2),
            "pr": round(daily_pr_percentage, 2),
            "poa": round(avg_poa, 2),
            "generation_monthly": round(month_gen_value, 2),
            "pr_monthly": round(month_pr_value, 2),
            "poa_monthly": round(monthly_poa_value, 2),
            "approved": 0,
            "review": 0,
            "action_performed": 0,
            "dgr_path": str(summary_pdf)
        }

        insert_solar_data_db([record])
        LOGGER.info(f"[DB Insert] Solar data saved for {plant_name}")

        # --- Inverter-wise data integration ---
        from datetime import datetime

        try:
            # Extract inverter-wise dataframes
            pr_df = data.get("pr_data_inverter")
            gen_df = data.get("daily_generation_inverter")
            pr_monthly_df = data.get("pr_data_inverter_monthly")
            gen_monthly_df = data.get("daily_generation_inverter_monthly")

            print("PR DF:", pr_df)
            print("GEN DF:", gen_df)
            print("PR MONTHLY DF:", pr_monthly_df)
            print("GEN MONTHLY DF:", gen_monthly_df)
            

            # Build inverter_names from all available columns in daily and monthly PR/GEN dataframes
            inverter_names = set()
            for df, suffix in [
                (pr_df, ".PR"),
                (gen_df, ".Daily Energy"),
                (pr_monthly_df, ".PR"),
                (gen_monthly_df, ".Daily Energy"),
            ]:
                if df is not None:
                    inverter_names.update([col.replace(suffix, "") for col in df.columns if col.endswith(suffix)])
            inverter_names = sorted(inverter_names)

            inverter_records = []
            for inv in inverter_names:
                pr = float(pr_df[f"{inv}.PR"].mean()) if pr_df is not None and f"{inv}.PR" in pr_df.columns else 0
                generation = float(gen_df[f"{inv}.Daily Energy"].sum()) if gen_df is not None and f"{inv}.Daily Energy" in gen_df.columns else 0
                pr_monthly = float(pr_monthly_df[f"{inv}.PR"].mean()) if pr_monthly_df is not None and f"{inv}.PR" in pr_monthly_df.columns else 0
                generation_monthly = float(gen_monthly_df[f"{inv}.Daily Energy"].sum()) if gen_monthly_df is not None and f"{inv}.Daily Energy" in gen_monthly_df.columns else 0

                inverter_record = {
                    "date": start_date,
                    "plant_id": plant_name,
                    "plant_name": customer_name,
                    "inverter_name": inv,
                    "generation": generation,
                    "pr": pr,
                    "poa": avg_poa,
                    "generation_monthly": generation_monthly,
                    "pr_monthly": pr_monthly,
                    "poa_monthly": monthly_poa_value,
                    "created_at": datetime.now(),
                    "updated_at": datetime.now()
                }
                inverter_records.append(inverter_record)
                print("INVERTER", inverter_record)
            if inverter_records:
                insert_solar_inverter_data_db(inverter_records)
                LOGGER.info(f"[Success] Inverter-wise data inserted for {plant_name} ({len(inverter_records)} inverters)")
        except Exception as e:
            LOGGER.error(f"[Error] Inverter-wise data insertion failed: {e}", exc_info=True)
            # Fallback: insert zeroed-out inverter records
            try:
                inverter_names = []
                if pr_df is not None:
                    inverter_names = [col.replace(".PR", "") for col in pr_df.columns if col.endswith(".PR")]
                elif gen_df is not None:
                    inverter_names = [col.replace(".Daily Energy", "") for col in gen_df.columns if col.endswith(".Daily Energy")]
                from datetime import datetime
                zero_inverter_records = []
                for inv in inverter_names:
                    zero_inverter_records.append({
                        "date": start_date,
                        "plant_id": plant_name,
                        "plant_name": customer_name,
                        "inverter_name": inv,
                        "generation": 0,
                        "pr": 0,
                        "poa": 0,
                        "generation_monthly": 0,
                        "pr_monthly": 0,
                        "poa_monthly": 0,
                        "created_at": datetime.now(),
                        "updated_at": datetime.now()
                    })
                if zero_inverter_records:
                    insert_solar_inverter_data_db(zero_inverter_records)
                    LOGGER.info(f"[Fallback] Zero inverter data inserted for {plant_name} ({len(zero_inverter_records)} inverters)")
            except Exception as e2:
                LOGGER.error(f"[DB Error] Failed inserting zero inverter data for {plant_name}: {e2}", exc_info=True)

        return summary_pdf

    except Exception as e:
        LOGGER.error(f"[Error] Failed generating report for {plant_name}: {e}", exc_info=True)
        _insert_zero_solar_data(plant_name, start_date, customer_name)
        return None




def _insert_zero_solar_data(plant_name: str, date: str, customer_name: str) -> None:
    """
    Insert a zeroed record into DB when solar report generation fails.
    """
    final_pdf_path = STATIC_REPORT_DIR / f"{plant_name}_DGR_{date}.jpg"
    zero_record = {
        "date": date,
        "plant_short_name": plant_name,
        "plant_long_name": customer_name,
        "generation": 0,
        "pr": 0,
        "poa": 0,
        "generation_monthly": 0,
        "pr_monthly": 0,
        "poa_monthly": 0,
        "approved": 0,
        "review": 0,
        "action_performed": 0,
        "dgr_path": str(final_pdf_path)
    }
    try:
        insert_solar_data_db([zero_record])
        LOGGER.info(f"[Fallback] Zero solar data inserted for {plant_name}")
    except Exception as e:
        LOGGER.error(f"[DB Error] Failed inserting zero solar data: {e}", exc_info=True)



# import os
# from pathlib import Path
# from typing import Dict, Any, Optional

# import pandas as pd

# from helper.utils import (
#     generate_solar_dgr_pdf,
#     get_dynamic_dates,
#     fetch_data_total,
#     get_capacity_from_csv,
#     _safe_mean,
#     _safe_sum,
# )
# from helper.logger_setup import setup_logger
# from helper.storage_s3 import upload_file_s3
# from DB.db_ops import insert_solar_data_db, insert_solar_inverter_data_db

# # -------------------------------
# # Constants
# # -------------------------------
# STATIC_REPORT_DIR = Path("static") / "solar_final_report"
# S3_REPORT_PREFIX = "solar_reports/"

# LOGGER = setup_logger("solar_automation", "solar_automation.log")


# # -------------------------------
# # Data Fetching
# # -------------------------------
# def fetch_all_solar_data(
#     plant_name: str,
#     start_date: str,
#     current_month_start: str,
#     last_30_days_start: str,
#     current_year_start: str,
#     yearly: str,
#     condition_poa: Dict[str, str],
#     condition_pr: Dict[str, str],
#     condition_generation: Dict[str, str],
#     condition_monthly_pr: Dict[str, str]
# ) -> Dict[str, pd.DataFrame]:
#     """
#     Fetch all solar data sequentially to reduce RAM usage.
#     """
#     LOGGER.info(f"[Data Fetch] Plant: {plant_name}")

#     return {
#         "poa_data": fetch_data_total(plant_name, ["Daily POA Energy"], "Plant", start_date, start_date, condition_poa),
#         "pr_data": fetch_data_total(plant_name, ["PR"], "Plant", start_date, start_date, condition_pr),
#         "daily_generation": fetch_data_total(plant_name, ["Daily Energy"], "Plant", start_date, start_date, condition_generation),
#         "generation_monthly_value": fetch_data_total(plant_name, ["Daily Energy"], "Plant", current_month_start, start_date, condition_generation),
#         "pr_data_monthly_value": fetch_data_total(plant_name, ["PR"], "Plant", current_month_start, start_date, condition_monthly_pr),
#         "poa_data_monthly_value": fetch_data_total(plant_name, ["Daily POA Energy"], "Plant", current_month_start, start_date, condition_poa),



#         "pr_data_inverter": fetch_data_total(plant_name, ["PR"], "Inverter", start_date, start_date, condition_pr),
#         "daily_generation_inverter": fetch_data_total(plant_name, ["Daily Energy"], "Inverter", start_date, start_date, condition_generation),
#         "pr_data_inverter_monthly": fetch_data_total(plant_name, ["PR"], "Inverter", current_month_start, start_date, condition_monthly_pr),
#         "daily_generation_inverter_monthly": fetch_data_total(plant_name, ["Daily Energy"], "Inverter", current_month_start, start_date, condition_generation),
#     }


# # -------------------------------
# # Main Report Generation
# # -------------------------------
# # -------------------------------
# # Paired Plants Mapping
# # -------------------------------
# paired_plants = {
#     "IN.INTE.SKRT": "IN.INTE.SKRT2"
# }

# def generate_solar_automation_report(
#     plant_name: str,
#     start_date: str,
#     customer_name: str,
#     project: str
# ) -> Optional[Path]:
#     """
#     Generate and upload a daily solar automation report for a given plant.
#     Handles paired plants by combining KPIs for both plants.
#     """
#     LOGGER.info(f"[Start Report] Plant: {plant_name}, Date: {start_date}, Customer: {customer_name}")

#     try:
#         # Define fetch conditions
#         condition_poa = {"Daily POA Energy": "last"}
#         condition_daily_pr = {"PR": "last"}
#         condition_monthly_pr = {"PR": "mean"}
#         condition_generation = {"Daily Energy": "max"}

#         # Get date ranges
#         current_month_start, _, _, _, _, last_30_days_start, current_year_start, last_year_date = get_dynamic_dates(start_date)

#         # Handle paired plants: if plant_name is in paired_plants, fetch both and combine
#         if plant_name in paired_plants:
#             plant_1 = plant_name
#             plant_2 = paired_plants[plant_name]

#             # Set customer_name for paired plants
#             paired_customer_name = "Stovekraft Limited Unit 1 & 2"

#             # Fetch all required data for both plants
#             data_1 = fetch_all_solar_data(
#                 plant_1, start_date, current_month_start, last_30_days_start,
#                 current_year_start, last_year_date, condition_poa, condition_daily_pr,
#                 condition_generation, condition_monthly_pr
#             )
#             data_2 = fetch_all_solar_data(
#                 plant_2, start_date, current_month_start, last_30_days_start,
#                 current_year_start, last_year_date, condition_poa, condition_daily_pr,
#                 condition_generation, condition_monthly_pr
#             )

#             # Extract and combine key metrics
#             total_generation = _safe_sum(data_1["daily_generation"]) + _safe_sum(data_2["daily_generation"])
#             month_gen_value = _safe_sum(data_1["generation_monthly_value"]) + _safe_sum(data_2["generation_monthly_value"])
#             month_pr_value = (_safe_mean(data_1["pr_data_monthly_value"]) + _safe_mean(data_2["pr_data_monthly_value"])) / 2
#             daily_pr_percentage = (_safe_mean(data_1["pr_data"]) + _safe_mean(data_2["pr_data"])) / 2
#             monthly_poa_value = (_safe_mean(data_1["poa_data_monthly_value"]) + _safe_mean(data_2["poa_data_monthly_value"])) / 2
#             avg_poa = (_safe_mean(data_1["poa_data"]) + _safe_mean(data_2["poa_data"])) / 2

#             capacity = get_capacity_from_csv(plant_1) + get_capacity_from_csv(plant_2)

#             # Prepare report path (use combined name)
#             combined_name = f"{plant_1}&{plant_2}"
#             STATIC_REPORT_DIR.mkdir(parents=True, exist_ok=True)
#             final_pdf_path = STATIC_REPORT_DIR / f"{combined_name}_DGR_{start_date}.jpg"

#             # Generate PDF report
#             summary_pdf = generate_solar_dgr_pdf(
#                 start_date, paired_customer_name, avg_poa, daily_pr_percentage,
#                 total_generation, month_gen_value, month_pr_value,
#                 monthly_poa_value, capacity, final_pdf_path, comment_text=None
#             )

#             LOGGER.info(f"[Report Generated] {summary_pdf}")

#             # Upload to S3
#             s3_path = f"{S3_REPORT_PREFIX}{combined_name}_DGR_{start_date}.jpg"
#             upload_file_s3(summary_pdf, s3_path)

#             # Insert into DB
#             record = {
#                 "date": start_date,
#                 "plant_short_name": combined_name,
#                 "plant_long_name": paired_customer_name,
#                 "generation": round(total_generation, 2),
#                 "pr": round(daily_pr_percentage, 2),
#                 "poa": round(avg_poa, 2),
#                 "generation_monthly": round(month_gen_value, 2),
#                 "pr_monthly": round(month_pr_value, 2),
#                 "poa_monthly": round(monthly_poa_value, 2),
#                 "approved": 0,
#                 "review": 0,
#                 "action_performed": 0,
#                 "dgr_path": str(summary_pdf)
#             }

#             insert_solar_data_db([record])
#             LOGGER.info(f"[DB Insert] Solar data saved for {combined_name}")

#             # --- Inverter-wise data integration for both plants ---
#             from datetime import datetime

#             try:
#                 inverter_records = []
#                 for idx, (data, plant) in enumerate([(data_1, plant_1), (data_2, plant_2)]):
#                     pr_df = data.get("pr_data_inverter")
#                     gen_df = data.get("daily_generation_inverter")
#                     pr_monthly_df = data.get("pr_data_inverter_monthly")
#                     gen_monthly_df = data.get("daily_generation_inverter_monthly")

#                     inverter_names = set()
#                     for df, suffix in [
#                         (pr_df, ".PR"),
#                         (gen_df, ".Daily Energy"),
#                         (pr_monthly_df, ".PR"),
#                         (gen_monthly_df, ".Daily Energy"),
#                     ]:
#                         if df is not None:
#                             inverter_names.update([col.replace(suffix, "") for col in df.columns if col.endswith(suffix)])
#                     inverter_names = sorted(inverter_names)

#                     for inv in inverter_names:
#                         pr = float(pr_df[f"{inv}.PR"].mean()) if pr_df is not None and f"{inv}.PR" in pr_df.columns else 0
#                         generation = float(gen_df[f"{inv}.Daily Energy"].sum()) if gen_df is not None and f"{inv}.Daily Energy" in gen_df.columns else 0
#                         pr_monthly = float(pr_monthly_df[f"{inv}.PR"].mean()) if pr_monthly_df is not None and f"{inv}.PR" in pr_monthly_df.columns else 0
#                         generation_monthly = float(gen_monthly_df[f"{inv}.Daily Energy"].sum()) if gen_monthly_df is not None and f"{inv}.Daily Energy" in gen_monthly_df.columns else 0

#                         inverter_record = {
#                             "date": start_date,
#                             "plant_id": plant,
#                             "plant_name": paired_customer_name,
#                             "inverter_name": inv,
#                             "generation": generation,
#                             "pr": pr,
#                             "poa": avg_poa,
#                             "generation_monthly": generation_monthly,
#                             "pr_monthly": pr_monthly,
#                             "poa_monthly": monthly_poa_value,
#                             "created_at": datetime.now(),
#                             "updated_at": datetime.now()
#                         }
#                         inverter_records.append(inverter_record)
#                 if inverter_records:
#                     insert_solar_inverter_data_db(inverter_records)
#                     LOGGER.info(f"[Success] Inverter-wise data inserted for {combined_name} ({len(inverter_records)} inverters)")
#             except Exception as e:
#                 LOGGER.error(f"[Error] Inverter-wise data insertion failed: {e}", exc_info=True)
#                 # Fallback: insert zeroed-out inverter records
#                 try:
#                     inverter_names = []
#                     if pr_df is not None:
#                         inverter_names = [col.replace(".PR", "") for col in pr_df.columns if col.endswith(".PR")]
#                     elif gen_df is not None:
#                         inverter_names = [col.replace(".Daily Energy", "") for col in gen_df.columns if col.endswith(".Daily Energy")]
#                     from datetime import datetime
#                     zero_inverter_records = []
#                     for inv in inverter_names:
#                         zero_inverter_records.append({
#                             "date": start_date,
#                             "plant_id": combined_name,
#                             "plant_name": paired_customer_name,
#                             "inverter_name": inv,
#                             "generation": 0,
#                             "pr": 0,
#                             "poa": 0,
#                             "generation_monthly": 0,
#                             "pr_monthly": 0,
#                             "poa_monthly": 0,
#                             "created_at": datetime.now(),
#                             "updated_at": datetime.now()
#                         })
#                     if zero_inverter_records:
#                         insert_solar_inverter_data_db(zero_inverter_records)
#                         LOGGER.info(f"[Fallback] Zero inverter data inserted for {combined_name} ({len(zero_inverter_records)} inverters)")
#                 except Exception as e2:
#                     LOGGER.error(f"[DB Error] Failed inserting zero inverter data for {combined_name}: {e2}", exc_info=True)

#             return summary_pdf

#         # If not a paired plant, proceed as before
#         # Fetch all required data
#         data = fetch_all_solar_data(
#             plant_name, start_date, current_month_start, last_30_days_start,
#             current_year_start, last_year_date, condition_poa, condition_daily_pr,
#             condition_generation, condition_monthly_pr
#         )

#         # Extract and compute key metrics
#         total_generation = _safe_sum(data["daily_generation"])
#         month_gen_value = _safe_sum(data["generation_monthly_value"])
#         month_pr_value = _safe_mean(data["pr_data_monthly_value"])
#         daily_pr_percentage = _safe_mean(data["pr_data"])
#         monthly_poa_value = _safe_mean(data["poa_data_monthly_value"])
#         avg_poa = _safe_mean(data["poa_data"])

#         capacity = get_capacity_from_csv(plant_name)

#         # Prepare report path
#         STATIC_REPORT_DIR.mkdir(parents=True, exist_ok=True)
#         final_pdf_path = STATIC_REPORT_DIR / f"{plant_name}_DGR_{start_date}.jpg"

#         # Generate PDF report
#         summary_pdf = generate_solar_dgr_pdf(
#             start_date, customer_name, avg_poa, daily_pr_percentage,
#             total_generation, month_gen_value, month_pr_value,
#             monthly_poa_value, capacity, final_pdf_path, comment_text=None
#         )

#         LOGGER.info(f"[Report Generated] {summary_pdf}")

#         # Upload to S3
#         s3_path = f"{S3_REPORT_PREFIX}{plant_name}_DGR_{start_date}.jpg"
#         upload_file_s3(summary_pdf, s3_path)

#         # Insert into DB
#         record = {
#             "date": start_date,
#             "plant_short_name": plant_name,
#             "plant_long_name": customer_name,
#             "generation": round(total_generation, 2),
#             "pr": round(daily_pr_percentage, 2),
#             "poa": round(avg_poa, 2),
#             "generation_monthly": round(month_gen_value, 2),
#             "pr_monthly": round(month_pr_value, 2),
#             "poa_monthly": round(monthly_poa_value, 2),
#             "approved": 0,
#             "review": 0,
#             "action_performed": 0,
#             "dgr_path": str(summary_pdf)
#         }

#         insert_solar_data_db([record])
#         LOGGER.info(f"[DB Insert] Solar data saved for {plant_name}")

#         # --- Inverter-wise data integration ---
#         from datetime import datetime

#         try:
#             # Extract inverter-wise dataframes
#             pr_df = data.get("pr_data_inverter")
#             gen_df = data.get("daily_generation_inverter")
#             pr_monthly_df = data.get("pr_data_inverter_monthly")
#             gen_monthly_df = data.get("daily_generation_inverter_monthly")

#             # Build inverter_names from all available columns in daily and monthly PR/GEN dataframes
#             inverter_names = set()
#             for df, suffix in [
#                 (pr_df, ".PR"),
#                 (gen_df, ".Daily Energy"),
#                 (pr_monthly_df, ".PR"),
#                 (gen_monthly_df, ".Daily Energy"),
#             ]:
#                 if df is not None:
#                     inverter_names.update([col.replace(suffix, "") for col in df.columns if col.endswith(suffix)])
#             inverter_names = sorted(inverter_names)

#             inverter_records = []
#             for inv in inverter_names:
#                 pr = float(pr_df[f"{inv}.PR"].mean()) if pr_df is not None and f"{inv}.PR" in pr_df.columns else 0
#                 generation = float(gen_df[f"{inv}.Daily Energy"].sum()) if gen_df is not None and f"{inv}.Daily Energy" in gen_df.columns else 0
#                 pr_monthly = float(pr_monthly_df[f"{inv}.PR"].mean()) if pr_monthly_df is not None and f"{inv}.PR" in pr_monthly_df.columns else 0
#                 generation_monthly = float(gen_monthly_df[f"{inv}.Daily Energy"].sum()) if gen_monthly_df is not None and f"{inv}.Daily Energy" in gen_monthly_df.columns else 0

#                 inverter_record = {
#                     "date": start_date,
#                     "plant_id": plant_name,
#                     "plant_name": customer_name,
#                     "inverter_name": inv,
#                     "generation": generation,
#                     "pr": pr,
#                     "poa": avg_poa,
#                     "generation_monthly": generation_monthly,
#                     "pr_monthly": pr_monthly,
#                     "poa_monthly": monthly_poa_value,
#                     "created_at": datetime.now(),
#                     "updated_at": datetime.now()
#                 }
#                 inverter_records.append(inverter_record)
#             if inverter_records:
#                 insert_solar_inverter_data_db(inverter_records)
#                 LOGGER.info(f"[Success] Inverter-wise data inserted for {plant_name} ({len(inverter_records)} inverters)")
#         except Exception as e:
#             LOGGER.error(f"[Error] Inverter-wise data insertion failed: {e}", exc_info=True)
#             # Fallback: insert zeroed-out inverter records
#             try:
#                 inverter_names = []
#                 if pr_df is not None:
#                     inverter_names = [col.replace(".PR", "") for col in pr_df.columns if col.endswith(".PR")]
#                 elif gen_df is not None:
#                     inverter_names = [col.replace(".Daily Energy", "") for col in gen_df.columns if col.endswith(".Daily Energy")]
#                 from datetime import datetime
#                 zero_inverter_records = []
#                 for inv in inverter_names:
#                     zero_inverter_records.append({
#                         "date": start_date,
#                         "plant_id": plant_name,
#                         "plant_name": customer_name,
#                         "inverter_name": inv,
#                         "generation": 0,
#                         "pr": 0,
#                         "poa": 0,
#                         "generation_monthly": 0,
#                         "pr_monthly": 0,
#                         "poa_monthly": 0,
#                         "created_at": datetime.now(),
#                         "updated_at": datetime.now()
#                     })
#                 if zero_inverter_records:
#                     insert_solar_inverter_data_db(zero_inverter_records)
#                     LOGGER.info(f"[Fallback] Zero inverter data inserted for {plant_name} ({len(zero_inverter_records)} inverters)")
#             except Exception as e2:
#                 LOGGER.error(f"[DB Error] Failed inserting zero inverter data for {plant_name}: {e2}", exc_info=True)

#         return summary_pdf

#     except Exception as e:
#         LOGGER.error(f"[Error] Failed generating report for {plant_name}: {e}", exc_info=True)
#         _insert_zero_solar_data(plant_name, start_date, customer_name)
#         return None




# def _insert_zero_solar_data(plant_name: str, date: str, customer_name: str) -> None:
#     """
#     Insert a zeroed record into DB when solar report generation fails.
#     """
#     final_pdf_path = STATIC_REPORT_DIR / f"{plant_name}_DGR_{date}.jpg"
#     zero_record = {
#         "date": date,
#         "plant_short_name": plant_name,
#         "plant_long_name": customer_name,
#         "generation": 0,
#         "pr": 0,
#         "poa": 0,
#         "generation_monthly": 0,
#         "pr_monthly": 0,
#         "poa_monthly": 0,
#         "approved": 0,
#         "review": 0,
#         "action_performed": 0,
#         "dgr_path": str(final_pdf_path)
#     }
#     try:
#         insert_solar_data_db([zero_record])
#         LOGGER.info(f"[Fallback] Zero solar data inserted for {plant_name}")
#     except Exception as e:
#         LOGGER.error(f"[DB Error] Failed inserting zero solar data: {e}", exc_info=True)
